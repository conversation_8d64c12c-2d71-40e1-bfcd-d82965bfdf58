import logging
from queue import Queue, Empty, Full
from typing import List, Any, Optional, Tuple
import time
from dataclasses import dataclass, field
from datetime import datetime
from threading import Lock, RLock
from contextlib import contextmanager

logger = logging.getLogger(__name__)

@dataclass
class QueueMetrics:
    total_messages_processed: int = 0
    failed_operations: int = 0
    last_batch_size: int = 0
    last_process_time: float = 0
    creation_time: datetime = field(default_factory=datetime.now)
    queue_size: int = 0
    fill_percentage: float = 0

class ThreadSafeQueueManager:
    def __init__(self, queue: Queue):
        self._queue = queue
        self._metrics = QueueMetrics()
        self._lock = RLock()
        
    @contextmanager
    def _timed_operation(self):
        start_time = time.time()
        try:
            yield
        finally:
            with self._lock:
                self._metrics.last_process_time = time.time() - start_time

    def _update_queue_health(self) -> None:
        with self._lock:
            try:
                size = self._queue.qsize()
                capacity = self._queue.maxsize if self._queue.maxsize > 0 else float('inf')
                self._metrics.queue_size = size
                self._metrics.fill_percentage = (size / capacity * 100) if capacity != float('inf') else 0
                
                if self._metrics.fill_percentage > 80:
                    logger.warning(f"Queue filling up! {self._metrics.fill_percentage:.1f}% full ({self._metrics.queue_size} messages)")
            except Exception as e:
                logger.error(f"Error updating queue health: {str(e)}")
                self._metrics.queue_size = -1
                self._metrics.fill_percentage = -1

    def _process_batch(self, timeout: float = 30.0, max_batch_size: int = 15000) -> List[Any]:
        messages = []
        start_time = time.time()
        
        with self._lock:
            while (time.time() - start_time < timeout and len(messages) < max_batch_size):
                try:
                    message = self._queue.get_nowait()
                    messages.append(message)
                except Empty:
                    break
                except Exception as e:
                    logger.error(f"Error getting message from queue: {str(e)}")
                    break
            
            self._metrics.last_batch_size = len(messages)
            self._metrics.total_messages_processed += len(messages)
            
        return messages

    def _preserve_messages(self, messages: List[Any], max_retries: int = 3) -> int:
        failed_count = 0
        
        for message in messages:
            success = False
            
            for retry in range(max_retries):
                try:
                    if retry > 0:
                        with self._lock:
                            time.sleep(0.1 * (retry))
                    
                    with self._lock:
                        self._queue.put_nowait(message)
                        success = True
                        break
                except (Full, Exception) as e:
                    if retry == max_retries - 1:
                        logger.error(f"Failed to preserve message after {max_retries} retries: {str(e)}")
                        failed_count += 1
        
        with self._lock:
            self._metrics.failed_operations += failed_count
            
        return failed_count

    def get_messages(self, preserve: bool = True) -> List[Any]:
        try:
            with self._timed_operation():
                messages = self._process_batch()
                
                if preserve and messages:
                    self._preserve_messages(messages)
                
                self._update_queue_health()
                
                logger.info(f"Retrieved {len(messages)} messages from queue")
                return messages.copy()
                
        except Exception as e:
            logger.error(f"Error in get_messages: {str(e)}", exc_info=True)
            return []

    def get_metrics(self) -> dict:
        with self._lock:
            self._update_queue_health()
            
            return {
                "total_messages_processed": self._metrics.total_messages_processed,
                "failed_operations": self._metrics.failed_operations,
                "last_batch_size": self._metrics.last_batch_size,
                "last_process_time": self._metrics.last_process_time,
                "queue_size": self._metrics.queue_size,
                "queue_fill_percentage": self._metrics.fill_percentage,
                "uptime": (datetime.now() - self._metrics.creation_time).total_seconds()
            }