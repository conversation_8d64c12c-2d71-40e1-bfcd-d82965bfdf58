import logging
import json
import base64
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class Baseline:

    def __init__(self, baseline_data_queue, data_lock):
        """
        Initializes the DataManipulator class.

        """
        self.baseline_data_queue = baseline_data_queue
        self.data_lock = data_lock

    # Method to prepare payload for saving to the time series database
    def manipulate_data(self, full_message):
        """
        Manipulates incoming data and stores it as time series data.

        Args:
            data (dict): The incoming data to be manipulated.
        """
        try:
            encoded_data = base64.b64decode(full_message['data_base64'])
            data = json.loads(encoded_data)
            schedule_lines = data['message']['command']['data']['scheduleLine']
            created_at = datetime.strptime(data['message']['createdAt'], "%Y-%m-%dT%H:%M:%S.%fZ")
        except (json.JSONDecodeError, KeyError, base64.binascii.Error) as e:
            logger.error(f"Error decoding or parsing data: {e}")
            logger.error(f"Data is : {full_message}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during data decoding or parsing: {e}")
            return None

        payload = []
        try:
            for schedule in schedule_lines:
                records = schedule['record']
                latest_records = self.get_latest_records(records)
                time_series = []

                for record in latest_records:
                    start_datetime  = record['startDatetime']
                    date_value =  datetime.strptime(start_datetime, "%Y-%m-%dT%H:%M:%S.%fZ")
                    date_value__plus_15  = date_value + timedelta(minutes=15)
                    value = record['quantity']
                    tick_str = created_at

                    # for database
                    time_series.append({
                        "dateTimeTick": tick_str.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                        "dateTimeValue": date_value.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                        "dateTimeToValue": date_value__plus_15.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                        "numericValue": value
                    })
                try:
                    timeseries_name = "SIJ_Acroni_Zunanji_odjemalci_VozniRedSIJ"
                    if timeseries_name:
                        payload.append({
                            "timeSeriesName": timeseries_name,
                            "timeSeriesValueInputs": time_series
                        })
                    else:
                        logger.error(f"Missing or invalid 'Analytical_TS' mapping for topic '{full_message.get('subject', '')}'.")
                except KeyError as e:
                    logger.error(f"Key error while accessing 'Analytical_TS': {e}")
                    logger.error(f"Data is : {full_message}")
                except Exception as e:
                    logger.error(f"Unexpected error while processing 'Analytical_TS' for topic '{full_message.get('subject', '')}': {e}")

        except KeyError as e:
            logger.error(f"Key error while processing schedule lines: {e}")
            logger.error(f"Data is : {data}")
            return None

        except Exception as e:
            logger.error(f"Error in processing data: {e}")
            logger.error(f"Data is : ' {data}")
            return None
        return payload

    def get_latest_records(self, data):
        for item in data:
            # Convert to datetime for sorting
            original_datetime_str = item['startDatetime']
            item['startDatetime'] = datetime.fromisoformat(original_datetime_str.replace('Z', '+00:00'))
            item['original_datetime_str'] = original_datetime_str

        data.sort(key=lambda x: x['startDatetime'])
        latest_records = {}
        for item in data:
            latest_records[item['startDatetime']] = item

        # Restore original datetime string and remove the temporary datetime object
        result = []
        for item in sorted(latest_records.values(), key=lambda x: x['startDatetime']):
            restored_item = item.copy()
            restored_item['startDatetime'] = restored_item['original_datetime_str']
            del restored_item['original_datetime_str']
            result.append(restored_item)

        return result




