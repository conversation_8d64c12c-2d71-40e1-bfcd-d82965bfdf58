import json
import logging
from datetime import datetime, timedelta, timezone
from queue import Queue, Full, Empty

from baseline import Baseline
from meterGatherer import MeterGatherer
from MQTTGatherer import MQ<PERSON>Gatherer

logger = logging.getLogger(__name__)


class Orchestrator:
    def __init__(self, timeseries_data_queue, baseline_data_queue, data_lock, data_lock_ravne, dl_baseline, timeseries_queue_ravne):
        self.timeseries_data_queue = timeseries_data_queue
        self.timeseries_queue_ravne = timeseries_queue_ravne
        self.baseline_data_queue = baseline_data_queue
        self.data_lock = data_lock
        self.data_lock_ravne = data_lock_ravne
        self.dl_baseline = dl_baseline

    def orchestrate(self, data):
        try:
            found = self.search_dict(data, "subject")
            if found:
                topic = data["subject"]
                if topic == 'acroni-measurements-mqtt/zds/proizvodnja' or topic == 'acroni-measurements-mqtt/zds/odjem' \
                    or topic == "acroni-measurements-mqtt/Metal" or topic == "acroni-measurements-mqtt/RavneSystem":
                    measurements = MQTTGatherer(self.timeseries_queue_ravne, self.data_lock_ravne)
                    measurements.gatherTS(data)
                elif "Zunanji_odjemalci_VozniRedSIJ" in topic:
                    baseline = Baseline(self.baseline_data_queue, self.data_lock)
                    payload = baseline.manipulate_data(data)
                    self.process_payload_array(payload)
                    self.remove_old_data()
                else:
                    return
            else:
                measurements = MeterGatherer(self.timeseries_data_queue, self.timeseries_queue_ravne, self.data_lock, self.data_lock_ravne)
                measurements.gatherTS(data)

        except Exception as e:
            logger.error(f"Error in processing data: {e}")
            logger.error(f"Data is: {data}")
            return

    def search_dict(self, d, search_str):
        d_str = json.dumps(d)
        return search_str in d_str

    def process_payload_array(self, payload_array):
        if isinstance(payload_array, str):
            payload_array = json.loads(payload_array)
        for payload in payload_array:
            timeseries_name = payload["timeSeriesName"]
            time_series_value_inputs = payload["timeSeriesValueInputs"]
            result = self.convert_power_to_energy(time_series_value_inputs)
            for value_input in result:
                dateValue = value_input["dateTimeTick"]
                numericValue = value_input["numericValue"]
                self.add_to_timeseries_queue(timeseries_name, dateValue, numericValue)

    def add_to_timeseries_queue(self, timeseries_name, time, numeric_value):
        if not timeseries_name:
            logger.warning(f"Attempted to add data with empty timeseries_name: {time}")
            return
            
        timeseries_data = (timeseries_name, time, numeric_value)
        try:
            logger.warning(f"Orchestrator Attempting to add data to queue for timeseries: {timeseries_name}")
            self.baseline_data_queue.put(timeseries_data, timeout=1.0)
        except Full:
            logger.warning(f"Orchestrator Queue is full - dropping data point for timeseries: {timeseries_name}")


    def convert_power_to_energy(self, power_data):
        result = []
        cumulative_energy_kwh = 0

        for entry in power_data:
            power_mw = entry["numericValue"]  # Power in MW
            start_time = datetime.strptime(
                entry["dateTimeValue"], "%Y-%m-%dT%H:%M:%S.%fZ"
            )
            end_time = datetime.strptime(
                entry["dateTimeToValue"], "%Y-%m-%dT%H:%M:%S.%fZ"
            )

            # Calculate energy increment for each 5-second interval
            energy_increment_kwh = power_mw * 1000 * (5 / 3600)  # kWh for 5 seconds

            # Calculate energy for each 5-second interval within this 15-minute period
            current_time = start_time
            while current_time < end_time:
                cumulative_energy_kwh += energy_increment_kwh

                result.append(
                    {
                        "dateTimeTick": current_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                        "dateTimeValue": current_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                        "dateTimeToValue": (
                            current_time + timedelta(seconds=5)
                        ).strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                        "numericValue": cumulative_energy_kwh,
                    }
                )

                current_time += timedelta(seconds=5)

        return result

    def remove_old_data(self):
        logger.warning("Starting cleanup of old data from queue BASELINE")
        today = datetime.now(timezone.utc).replace(minute=0, second=0, microsecond=0)
        items_to_requeue = []
        
        while True:
            try:
                item = self.baseline_data_queue.get_nowait()
                date_time_value = datetime.strptime(
                    item[1], 
                    "%Y-%m-%dT%H:%M:%S.%fZ"
                ).replace(tzinfo=timezone.utc)
                
                if date_time_value >= today:
                    logger.warning(f"Found valid data point from {date_time_value} for timeseries: {item[0]}")
                    items_to_requeue.append(item)
                else:
                    logger.warning(f"Removing expired data point from {date_time_value} for timeseries: {item[0]}")
            except Empty:
                logger.warning("Finished processing all items in queue")
                break
        
        logger.warning(f"Requeueing {len(items_to_requeue)} valid items")
        for item in items_to_requeue:
            logger.warning(f"Requeueing data point for timeseries: {item[0]}")
            self.baseline_data_queue.put(item)
        logger.warning("Cleanup operation completed")

