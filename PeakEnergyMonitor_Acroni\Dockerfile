FROM python:3.13-slim-bullseye

# Set metadata for the image
LABEL maintainer="<PERSON><PERSON><PERSON> <du<PERSON>.<PERSON><PERSON><PERSON><PERSON>@kolektor.com>"

# Copy pip configuration file
COPY ./pip.ini /etc/pip.conf

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    USER=app

ENV HOME=/home/<USER>
    PATH=/usr/local/bin:$PATH


# Create a non-root user and set permissions
RUN adduser --uid 2000 --gecos "" --disabled-password $USER \
    && mkdir -p $HOME/scripts \
    && chown -R $USER:$USER $HOME

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends gcc  libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Switch to non-root user
USER $USER

# Copy requirements and install dependencies
COPY ./app/requirements.txt ./
RUN python -m pip install --upgrade pip \
    && pip install -U --no-cache-dir --no-warn-script-location -r requirements.txt

# Copy application scripts
COPY ./app/scripts $HOME/scripts

# Set the working directory
WORKDIR $HOME/scripts

RUN rm -f $HOME/scripts/acroni-peak-config.json

# Expose the port the app runs on
EXPOSE 8125

# Set default command to run the application
CMD ["python", "peakMonitor.py"]
