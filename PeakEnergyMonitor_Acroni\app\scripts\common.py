from datetime import datetime, timedelta, timezone
from zoneinfo import ZoneInfo

class Common:
    @staticmethod
    def calculate_utc_time(dt, bits, minutes, extra_minutes=0):
        """
        Adjusts a timestamp according to specified adjustment and extra minutes.
        
        Args:
            timestamp_dt (datetime): Input timestamp to adjust
            adjustment_minutes (int): Base adjustment in minutes (e.g., 0 or 60)
            extra_minutes (int): Additional adjustment in minutes (-15, 0, or 15)
        
        Returns:
            datetime: Adjusted UTC timestamp
        """        
        try:
            if extra_minutes not in [-15, 0, 15]:
                raise ValueError("extra_minutes must be -15, 0, or 15")
            if minutes not in [0, 60]:
                raise ValueError("adjustment_minutes must be 0 or 60")            
            try:
                timestamp_dt = datetime.strptime(dt, "%m/%d/%Y %H:%M:%S %Z%z")
            except ValueError:
                timestamp_dt = datetime.strptime(dt, "%m/%d/%Y %H:%M:%S")
                timestamp_dt = timestamp_dt.replace(tzinfo=ZoneInfo("Europe/Ljubljana"))
            
            # Get bit8 (rightmost bit)
            bit8 = (bits >> 7) & 1  # Using bitwise AND to get the last bit
                        
            # Calculate the initial time adjustment in minutes
            adjustment_minutes = bit8 * minutes
            
            if adjustment_minutes == 0:
                total_adjustment_minutes = extra_minutes
            elif adjustment_minutes == 60:
                if extra_minutes == -15:
                    total_adjustment_minutes = -75  # Backward by 75 minutes
                elif extra_minutes == 0:
                    total_adjustment_minutes = -60  # Backward by 60 minutes
                else:  # extra_minutes == 15
                    total_adjustment_minutes = -45  # Backward by 45 minutes
    
            utc_time = timestamp_dt.astimezone(ZoneInfo("UTC")) + timedelta(minutes=total_adjustment_minutes)


            return utc_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        
        except KeyError as e:
            raise KeyError(f"Missing required field in data structure: {e}")
        except ValueError as e:
            raise ValueError(f"Error parsing timestamp: {e}")
        except Exception as e:
            raise Exception(f"Error parsing timestamp: {e}")
