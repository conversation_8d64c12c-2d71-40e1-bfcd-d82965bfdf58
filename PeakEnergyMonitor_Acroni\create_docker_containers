docker run -d --name=telegraf --network=telegraph_network -v /Users/<USER>/Library/CloudStorage/OneDrive-Kolektor/Flexibility\ platform/Software/Setup.peak/SCADA/ua/telegraf.conf:/etc/telegraf/telegraf.conf telegraf

docker run -d --name=acroni-peak -p 8125:8125 acroni-peak

az login

az acr login --name kolektorsetup

docker build -t acroni-peak .

docker build -f Dockerfile.dev -t acroni-peak .

docker buildx build --platform linux/amd64 --load -t acroni-peak .

docker tag sha256:d5d3ece83be6cc7599b6c0129bf6e9aff41bc960baf051ed3b7f634700ab7e81 kolektorsetup.azurecr.io/fsi/acroni-peak:acroni-peak-1.0.1

docker push kolektorsetup.azurecr.io/fsi/acroni-peak:acroni-peak-1.0.1


az aks get-credentials --resource-group setup-flexibility --name flexibility-dev

az aks get-credentials --resource-group setup-flexibility --name flexibility-prod


az login --tenant 8541dae1-904b-433f-a318-030780dbfe80

kubectl config set-context --current --namespace=fsi

kubectl describe telegraf-deployment-frr-mo-5456996577-gcmc5 -n fsi

kubectl logs acroni-peak-69775fdcc4-cn5r5 -c acroni-peak -n fsi


kubectl apply -f deployment-acroni-peak.yaml -n fsi

kubectl delete -f deployment-acroni-peak.yaml -n fsi

kubectl delete pod telegraf-deployment-frr-mo-55cc5bc889-ct7nx -n fsi

kubectl rollout restart deploy/acroni-peak -n fsi

Monitor:

kubectl get pods -n fsi

