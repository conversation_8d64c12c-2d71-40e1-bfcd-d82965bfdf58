import json
import logging
import os
import signal
import sys
import threading
import time
import types
from datetime import datetime, timedelta
from os import getenv
from queue import Queue
from typing import Dict, List
from sys import stdout

import jwt
import pandas as pd
import plotly.graph_objs as go
import simplejson as json
import uvicorn
from azure.core.exceptions import ResourceNotFoundError
from azure.eventhub import EventHubConsumerClient
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient
from azure.storage.blob import BlobLeaseClient, BlobServiceClient
from dash import Dash, dcc, html
from dash.dependencies import Input, Output
from fastapi import FastAPI
from ksLib.api.TsAPI import TsAPI
from ksLib.environment import Environment
from ksLib.other.AzureCredential import get_default_azure_credential
from orchestrator import Orchestrator
from peakProcessor import PeakProcessor
from peakProcessorRavne import PeakProcessorRavne
from ThedoraLogin import ThedoraLogin

# Set up Dash app
app_dash = Dash(__name__)

app_dash.layout = html.Div([
    dcc.Graph(id='live-graph'),
    dcc.Interval(
        id='graph-update',
        interval=10*1000,  # Update graph every second
        n_intervals=0
    )
])


# Initialize FastAPI app
app = FastAPI(docs_url=None, redoc_url=None)

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Global variables
event_hub_clients: Dict[str, Dict[str, EventHubConsumerClient]] = {}
consumer_threads: Dict[str, threading.Thread] = {}
config_curr = []
token = None

graph_data = []

# Create a thread-safe queue - for manipulating interval from to - meters return multiple times the same data
timeseries_data_queue = Queue(maxsize=100000)
# Create a lock for synchronizing access to the queue
data_lock = threading.RLock()
data_lock_ravne = threading.RLock()
dl_baseline = threading.RLock()

# Create a thread-safe queue - for baseline, drop and change it once a day
baseline_data_queue = Queue(maxsize=50000)

timeseries_ravne_queue = Queue(maxsize=50000)

acroni_power_current_interval = [0]
systems_power_current_interval = [0]
metal_power_current_interval = [0]

energy_before = [0]
previous_time = [0, 0, 0]
power_before = [0]
demag_energy_before = [0]
previous_demag_time = [0]
data_missing_alarm = [0, 0, 0]
excess = [0, 0, 0]
previous_uhp_energy = [0]
previous_uhp_timestamp = [0]
acroni_cap = [0]
ravne_cap = [0]

def diff_configs(new_config: List[dict], old_config: List[dict]) -> (set, set, set):

    changed_topics = set()
    new_topics = set()
    removed_topics = set()

    old_topic_configs = {
        config_item["event_hub_name"]: config_item
        for eventhub_config in old_config
        for config_item in eventhub_config.get("eventhub_details", [])
    }
    new_topic_configs = {
        config_item["event_hub_name"]: config_item
        for eventhub_config in new_config
        for config_item in eventhub_config.get("eventhub_details", [])
    }

    for topic, new_config in new_topic_configs.items():
        if topic not in old_topic_configs:
            new_topics.add(topic)
        elif new_config != old_topic_configs[topic]:
            changed_topics.add(topic)

    removed_topics = set(old_topic_configs.keys()) - \
        set(new_topic_configs.keys())

    return changed_topics, new_topics, removed_topics


def download_config_file(connect_str: str, container_name: str, blob_name: str):

    blob_service_client = BlobServiceClient.from_connection_string(connect_str)
    blob_client = blob_service_client.get_blob_client(
        container=container_name, blob=blob_name
    )
    with open(blob_name, "wb") as file:
        download_stream = blob_client.download_blob()
        file.write(download_stream.readall())


def load_config(local_file_path: str) -> List[dict]:
    with open(local_file_path, "r") as file:
        return json.load(file)


def read_config_file(connect_str: str, container_name: str, blob_name: str) -> List[dict]:
    try:
        blob_service_client = BlobServiceClient.from_connection_string(
            connect_str)
        blob_client = blob_service_client.get_blob_client(
            container=container_name, blob=blob_name
        )
        config_bytes = blob_client.download_blob().readall()
        config_string = config_bytes.decode("utf-8")
        config = json.loads(config_string)
        return config
    except ResourceNotFoundError as e:
        logger.error(
            f"Config file {blob_name} not found in Azure Blob Storage: {e}")
        return []
    except Exception as e:
        logger.error(
            f"Error occurred while reading config file from Azure Blob Storage: {e}"
        )
        return []


def load_config_from_string(config_string: str) -> List[dict]:
    try:
        return json.loads(config_string)
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON config string: {e}")
        return None


def get_starting_position(starting_position):

    if starting_position.startswith("@enqueued"):
        enqueued_time_str = starting_position.split("@enqueued=")[1]
        enqueued_time = datetime.strptime(enqueued_time_str, "%d.%m.%YT%H:%M:%S.%fZ")
        return enqueued_time
    elif starting_position == "@latest":
        return "@latest"
    elif starting_position == "-1":
        return starting_position
    else:
        return "offset:" + starting_position


def update_event_hub_consumers(config: List[dict]):
    global event_hub_clients, config_curr

    new_event_hub_clients = {}
    changed_event_hubs, new_event_hubs, removed_event_hubs = diff_configs(config, config_curr)

    # Stop and remove consumer threads for changed and removed topics
    for event_hub_name in changed_event_hubs | removed_event_hubs:
        try:
            for event_hub_name, (thread, stop_event) in consumer_threads.items():
                stop_event.set()  # Signal the thread to stop
                thread.join(timeout=5)  # Wait for 5 seconds
                if thread.is_alive():
                    logger.error(f"Failed to stop consumer thread for topic: {event_hub_name}")
        except KeyError:
            logger.error(f"No consumer thread found for topic: {event_hub_name}")

    # Stop and remove clients for changed and removed event hubs
    for event_hub_name in changed_event_hubs | removed_event_hubs:
        try:
            if event_hub_name in event_hub_clients:
                client = event_hub_clients[event_hub_name]
                client.close()
                del event_hub_clients[event_hub_name]
        except KeyError:
            logger.error(f"No client found for Event Hub: {event_hub_name}")

    for eventhub_config in config:
        eventhub_details = eventhub_config.get("eventhub_details")
        if eventhub_details:
            for config_item in eventhub_details:
                event_hub_name = config_item["event_hub_name"]
                connection_str = config_item["event_hub_connection"]
                consumer_group = config_item.get("consumer_group", "peak_monitor")   #repeat_consume & peak_monitor
                starting_position = config_item.get(
                    "starting_position", "@latest")
                num_messages = int(config_item["num_messages"])
                timeout = int(config_item["timeout"])

                if (event_hub_name in new_event_hubs or event_hub_name in changed_event_hubs):
                    client = EventHubConsumerClient.from_connection_string(
                        connection_str,
                        consumer_group=consumer_group,
                        eventhub_name=event_hub_name,
                    )
                    position = get_starting_position(starting_position)
                    stop_event = threading.Event()

                    consumer_thread = threading.Thread(name=f'Thread {event_hub_name}', target=_consume_messages,
                                    args=(client, num_messages, timeout, stop_event, position)
                    )
                    consumer_thread.start()
                    consumer_threads[event_hub_name] = (consumer_thread, stop_event)
                    if event_hub_name not in new_event_hub_clients:
                        new_event_hub_clients[event_hub_name] = client

    event_hub_clients = new_event_hub_clients
    if new_event_hubs or changed_event_hubs or removed_event_hubs:
        config_curr = config


def _consume_messages(client, num_messages, timeout, stop_event, starting_position):
    client.receive_batch(
        on_event_batch=on_event_batch(stop_event),
        max_batch_size=num_messages,
        max_wait_time=timeout,
        starting_position=starting_position
    )

def on_event_batch(stop_event):
    def _on_event_batch(partition_context, events):
        while not stop_event.is_set():
            try:
                if not isinstance(events, list):
                    events = [events]

                for event in events:
                    try:
                        body = event.body
                        if isinstance(body, types.GeneratorType):
                            raw_body = b''.join(body)
                        elif isinstance(body, bytes):
                            raw_body = body
                        else:
                            raw_body = str(body)
                        if isinstance(raw_body, bytes):
                            decoded_body = raw_body.decode('utf-8', errors='ignore')
                        else:
                            decoded_body = raw_body
                        message = json.loads(decoded_body, strict=False)
                        orchestrator = Orchestrator(timeseries_data_queue, baseline_data_queue, data_lock, data_lock_ravne, dl_baseline, timeseries_ravne_queue)
                        orchestrator.orchestrate(message)

                    except Exception as e:
                        logger.error(f"Error occurred while processing an event: {e}")
                        logger.error(f"Data is : '{event.body_as_str()}'")
                        continue

                break

            except Exception as e:
                logger.error(f"Error occurred while processing the event batch: {e}")
                return _on_event_batch

        return _on_event_batch

    return _on_event_batch



# Function to retrieve the connection string from Azure Key Vault
def retrieve_storage_connection_string(
        key_vault_url: str, credential: DefaultAzureCredential, secret_name: str) -> str:
    secret_client = SecretClient(vault_url=key_vault_url, credential=credential)
    secret = secret_client.get_secret(secret_name)
    return secret.value


# Event handler for monitoring changes in Azure Blob Storage
class BlobStorageEventHandler:
    def __init__(
        self, blob_client, connect_str, container_name, blob_name, update_function
    ):
        self.blob_client = blob_client
        self.connect_str = connect_str
        self.container_name = container_name
        self.blob_name = blob_name
        self.update_function = update_function
        self.lease_client = BlobLeaseClient(blob_client)  # Create lease client
        self.update_thread = None

    def check_for_changes(self, check_time, lease_duration):
        while True:
            try:
                self.lease_client.acquire(lease_duration)  # Acquire lease
                blob_properties = self.blob_client.get_blob_properties()
                current_time = time.time()
                if (current_time - blob_properties.last_modified.timestamp() < check_time):
                    download_config_file(self.connect_str, self.container_name, self.blob_name)
                    # Start update_function
                    self.update_function()
            except Exception as e:
                logger.error(f"Lease acquisition or processing failed: {e}")
            finally:
                try:
                    self.lease_client.release()  # Release the lease
                except Exception as e:
                    logger.error(f"Lease release failed: {e}")
            time.sleep(check_time)


def signal_handler(signal, frame, blob_storage_event_handler):
    logger.info(
        "Received signal to terminate. Stopping EventHub consumer threads, event_hub clients, and blob storage observer..."
    )

    try:
        for event_hub_name, (thread, stop_event) in consumer_threads.items():
            logger.info(f"Stopping consumer thread for topic: {event_hub_name}")
            stop_event.set()
            thread.join(timeout=5)
            if thread.is_alive():
                logger.error(f"Failed to stop consumer thread for topic: {event_hub_name}")
    except KeyError:
        logger.error(f"No consumer thread found for topic: {event_hub_name}")

    blob_storage_event_handler.stop()
    sys.exit(0)

def loginThedora():
    theodoraLogin = ThedoraLogin(
        url='https://oauth.thedora.io/realms/ProCom/protocol/openid-connect/token',
        user_name='apiuser_kolektor_sflex',
        password='qi*LmTz#4f36ySX4HC!BFBjHh6dgKUFJ',
        client_id='mixprice-client',
        client_secret='885dc5ac-08df-46e9-853e-a11f2570279f'
    )

    token = theodoraLogin.login()
    return token

def check_and_refresh_token(token):

    decoded_token = jwt.decode(token, options={"verify_signature": False})
    expiration_time = datetime.fromtimestamp(decoded_token['exp'])
    current_time = datetime.now()
    if expiration_time - current_time < timedelta(minutes=5):
        new_token = loginThedora()
        return new_token
    else:
        return token

def process_queue(token, ts_api):
    while True:
        try:
            logger.warning("Procesiram JEBENI ŠVABSKI TOKEN")
            token = check_and_refresh_token(token)
            logger.warning("REFRESHAL SEM JEBENI ŠVABSKI TOKEN")

            peakProcessor = PeakProcessor(
                token,
                timeseries_data_queue,
                baseline_data_queue,
                data_lock,
                graph_data,
                ts_api,
                acroni_power_current_interval,
                energy_before,
                previous_time,
                power_before,
                demag_energy_before,
                previous_demag_time,
                data_missing_alarm,
                excess,
                acroni_cap
            )
            logger.warning("Procesiram JEBENI Acroni")
            peakProcessor.process()
            logger.warning("KONEC procesiranja JEBENI Acroni")
            time.sleep(10)
            logger.warning("SEM ŠE POSLEEP-al JEBENI Acroni")

        except Exception as e:
            logger.error("Exception in process_queue: %s", e)
            time.sleep(10)

def process_queue_ravne(token, ts_api):
    while True:
        try:
            token = check_and_refresh_token(token)

            peakProcessor = PeakProcessorRavne(
                token,
                timeseries_ravne_queue,
                data_lock_ravne,
                graph_data,
                ts_api,
                previous_time,
                data_missing_alarm,
                timeseries_data_queue,
                systems_power_current_interval,
                metal_power_current_interval,
                excess,
                previous_uhp_energy,
                previous_uhp_timestamp,
                ravne_cap

            )
            peakProcessor.process()

            time.sleep(10)
        except Exception as e:
            logger.error("Exception in process_queue: %s", e)
            time.sleep(10)

def initialize_app(const_file_path: str, credential: DefaultAzureCredential):
    e = Environment()
    e.load_generic_environment_files()

    env = getenv("ENVIRONMENT")
    ts_api_url = getenv("TSAPI_API_URL")
    ts_api_key = getenv("TSAPI_API_KEY")
    ts_api = TsAPI(ts_api_url, ts_api_key, log=logger)
    const = load_config(const_file_path)
    container_name = const["container_name"]
    connect_str = retrieve_storage_connection_string(const["key_vault_url"], credential, container_name)

    blob_name = const["blob_name"]
    download_config_file(connect_str, container_name, blob_name)
    config = load_config(blob_name)
    time_details = config[1]["time_details"]
    check_time = int(time_details["check_time"])
    lease_duration = int(time_details["lease_duration"])

    if not config:
        logger.error("Failed to parse initial configuration. Exiting.")
        return

    token = loginThedora()
    global EventHub_consumers
    update_event_hub_consumers(config)

    blob_service_client = BlobServiceClient.from_connection_string(connect_str)
    blob_client = blob_service_client.get_blob_client(
        container=container_name, blob=blob_name
    )
    blob_storage_event_handler = BlobStorageEventHandler(
        blob_client,
        connect_str,
        container_name,
        blob_name,
        lambda: update_event_hub_consumers(load_config(blob_name)),
    )
    blob_storage_thread = threading.Thread(
        name=f'Thread-2 (_check_for_changes)',
        target=blob_storage_event_handler.check_for_changes,
        args=(check_time, lease_duration),
    )
    blob_storage_thread.start()

    queue_thread = threading.Thread(name='Thread-3 (_process_queue)_acroni', target=process_queue, args=(token, ts_api))
    queue_thread.start()

    queue_thread_ravne = threading.Thread(name='Thread-4 (_process_queue_ravne)', target=process_queue_ravne, args=(token, ts_api))
    queue_thread_ravne.start()

    signal.signal(
        signal.SIGTERM,
        lambda signal, frame: signal_handler(
            signal, frame, blob_storage_event_handler),
    )

@app_dash.callback(
    Output('live-graph', 'figure'),
    Input('graph-update', 'n_intervals')
)

def update_graph(n):
    with data_lock:
        if not graph_data:
            return go.Figure()

    df = pd.DataFrame(graph_data)

    if not df.empty:        # Convert the 'timestamp' column to datetime format
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        df['value'] = pd.to_numeric(df['value'], errors='coerce')
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=df['timestamp'], y=df['value'], mode='lines+markers', name='Value'))
        fig.update_layout(
            title='Real-time Peak Data',
            xaxis_title='Timestamp',
            yaxis_title='Value'
        )
    else:
        fig = go.Figure()
    return fig

def run_dash():
    app_dash.run_server(warning=True, use_reloader=False, port=8050)

@app.get("/health")
async def health_check():
    try:
        return {"status": "OK"}
    except Exception as e:
        logger.error(f"Error occurred during the health check: {e}")
        return {"status": "Error"}

if __name__ == "__main__" or os.getenv("RUN_MAIN"):
    uvicorn_thread = threading.Thread(name=f'Thread-1 (_health_check)',target=lambda: uvicorn.run(app, host="0.0.0.0", port=8125))
    uvicorn_thread.start()

    logger.warning("Starting APP 4.0...")

    #dash_thread = threading.Thread(target=run_dash, daemon=True)
    #dash_thread.start()

    #credential = DefaultAzureCredential()
    #credential = DefaultAzureCredential(managed_identity_client_id='27782d7e-0356-4731-9de2-c2abafa102e5')
    credential = get_default_azure_credential()

    initialize_app("const.json", credential)
