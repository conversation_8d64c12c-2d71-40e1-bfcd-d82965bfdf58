import json
import logging
from queue import Full
import time
from meter_E850 import MeterE850
from meter_MT880 import MeterMT880
from meter_MT880v2 import MeterMT880v2

logger = logging.getLogger(__name__)

class MeterGatherer:

    def __init__(self, timeseries_data_queue, timeseries_queue_ravne, data_lock, data_lock_ravne):
        """
        Initializes the MeterGatherer class.

        Parameters:
            timeseries_data_queue: A thread-safe queue to store timeseries data.
            data_lock (object): A lock for synchronizing access to the queue.
            ts_api: The instance of the TsAPI class for calling API functions.
        """
        self.timeseries_data_queue = timeseries_data_queue
        self.timeseries_queue_ravne = timeseries_queue_ravne
        self.data_lock = data_lock
        self.data_lock_ravne = data_lock_ravne
        self.valid_timeseries_names_acroni = [
            "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Acroni_SI_ACR_TR3_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaOddana",
            "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaOddana",
            "SIJ_Acroni_SI_ACR_TR3_KumulativnaDelovnaEnergijaOddana",
            "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaOddana",
            "SIJ_Acroni_Zunanji_odjemalci_VozniRedSIJ",
            "SIJ_Acroni_SI_DEMAG_KumulativnaDelovnaEnergijaOddana",
            "SIJ_Acroni_SI_DEMAG_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Acroni_SI_Daljnovod_Jesenice_Javornik_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Acroni_SI_Daljnovod_Jesenice_Javornik_KumulativnaDelovnaEnergijaOddana",
        ]
        self.valid_timeseries_names_ravne = [
            "SIJ_Metal_SI_VPP2_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Metal_SI_VPP1_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Metal_SI_UHP_J12_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Metal_SI_UHP_J12_KumulativnaDelovnaEnergijaOddana"
        ]
    # Method to prepare payload for saving to the time series database
    def gatherTS(self, data):
        """
        Manipulates incoming data and stores it as time series queue.

        Args:
            data (dict): The incoming data to be manipulated.
        """
        try:
            keys = list(data.keys())
            if not keys:
                logger.error(f"Data dictionary is empty + data = {data}")
                return
            append_ts = keys[0] + '_'
            data_json = data[keys[0]]
            if 'name' not in data_json:
                logger.error(f"Missing 'name' in data_json +  + data = {data}")
                return
            append_ts = keys[0] + '_'
            data_json = data[keys[0]]
            test = data_json['name']
            if test.upper() == 'DLMS_DATA_DAILY':
                return
            if test[0:9].upper() != 'DLMS_DATA':
                return
            data = data_json['data']
            if "MT880V2" in test.upper():
                meter = MeterMT880v2(self.data_lock, self.timeseries_data_queue)
                payload_array = meter.manipulate_data_MT880v2(data, append_ts)
                self.process_payload_array(payload_array)
                return

            # Search for "MT880"
            found = self.search_dict(data, "MT880")
            if found:
                meter = MeterMT880(self.data_lock, self.timeseries_data_queue)
                payload_array= meter.manipulate_data_MT880(data, append_ts)
                self.process_payload_array(payload_array)
                return

            # Search for "E850"
            found = self.search_dict(data, "E850")
            if found:
                meter = MeterE850(self.data_lock, self.timeseries_data_queue)
                payload_array = meter.manipulate_data_E850(data, append_ts)
                self.process_payload_array(payload_array)
                return

        except Exception as e:
            logger.error(f"Error manipulating data: {e} + data = {data}")
            return

    # Function to search for the string
    def search_dict(self, d, search_str):
        d_str = json.dumps(d)
        return search_str in d_str

    def process_payload_array(self, payload_array):
        batch_data_acroni = []
        batch_data_ravne = []
        
        for payload in payload_array:
            for item in payload:
                timeseries_name = item["timeSeriesName"]
                time_series_value_inputs = item["timeSeriesValueInputs"]
                
                data_tuple = [
                    (timeseries_name, value_input["dateTimeValue"], value_input["numericValue"])
                    for value_input in time_series_value_inputs
                ]
                
                if timeseries_name in self.valid_timeseries_names_acroni:
                    batch_data_acroni.extend(data_tuple)
                if timeseries_name in self.valid_timeseries_names_ravne:
                    batch_data_ravne.extend(data_tuple)
        
        if batch_data_acroni or batch_data_ravne:
            self.add_batch_to_timeseries_queues(batch_data_ravne, batch_data_acroni)

    def add_batch_to_timeseries_queues(self, batch_data_ravne, batch_data_acroni, max_retries=3):
        with self.data_lock:
            for timeseries_data in batch_data_acroni:
                for retry in range(max_retries):
                    try:
                        self.timeseries_data_queue.put_nowait(timeseries_data)
                        break
                    except Full:
                        if retry == max_retries - 1:
                            logger.error(f"Failed to add to timeseries_data_queue after {max_retries} retries")
                        time.sleep(0.1 * (retry + 1)) 
                    except Exception as e:
                        logger.error(f"Error adding to timeseries_data_queue: {str(e)}")
                        break

        with self.data_lock_ravne:
            for timeseries_data in batch_data_ravne:
                for retry in range(max_retries):
                    try:
                        self.timeseries_queue_ravne.put_nowait(timeseries_data)
                        break
                    except Full:
                        if retry == max_retries - 1:
                            logger.error(f"Failed to add to timeseries_queue_ravne after {max_retries} retries")
                        time.sleep(0.1 * (retry + 1)) 
                    except Exception as e:
                        logger.error(f"Error adding to timeseries_queue_ravne: {str(e)}")
                        break


