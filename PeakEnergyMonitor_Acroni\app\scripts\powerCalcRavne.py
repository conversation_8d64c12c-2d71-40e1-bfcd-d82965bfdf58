import logging
from datetime import datetime, timezone, timedelta
import pandas as pd

# Configure logging
logger = logging.getLogger(__name__)

class PowerCalcRavne:

    @staticmethod
    def peakSystems(ts: dict, messages: list, timestamp: datetime, previous_time: datetime,
                    interval_end: datetime, power_current_interval: list, data_missing_alarm: int, excess: int) -> tuple[float, float, float, int, int]:

        averageWPEndInterval = 0
        currentTrendEndInterval = 0
        averageWPInterval = 0
        energy, cur_time = PowerCalcRavne.currentEnergy(ts=ts, messages=messages)
        if timestamp and power_current_interval[0] > 0:
            averageWPEndInterval = PowerCalcRavne.processAveragePowerEndInterval(energy, timestamp, interval_end, power_current_interval[0])
        if previous_time:
            power = PowerCalcRavne.processCurrentWorkingPower(ts, messages)
            if power > 0:
                averageWPInterval, currentTrendEndInterval = PowerCalcRavne.processBillingPowerTrendEndoFfInterval(timestamp, interval_end, energy)

        #pres, excess = PowerCalcRavne.processRavneCurrentWorkingPowerWithoutUHP(power,timestamp,
        #                                                                        interval_end, energy, None,
        #                                                                        None, None, None, power_current_interval,
        #                                                                        data_missing_alarm, excess[2])
        pres = 0
        excess = 0
        return (averageWPEndInterval, currentTrendEndInterval, averageWPInterval, pres, excess)

    @staticmethod
    def peakMetal(ts: dict, current_energy_uhp: float, uhp_timestamp: datetime, messages: list, timestamp: datetime, previous_time: datetime,
                    interval_end: datetime, power_current_interval: list, data_missing_alarm: int, excess: int,
                    previous_uhp_energy: list, previous_uhp_timestamp: list) -> tuple[float, float, float, int, int, float, float]:
        averageWPEndInterval = 0
        currentTrendEndInterval = 0
        averageWPInterval = 0
        pres = 0
        endPower = 0
        uhp_power = 0

        energy, curr_time = PowerCalcRavne.cumulativeEnergy(ts=ts, messages=messages)
        #current_energy_uhp, uhp_timestamp = PowerCalcRavne.cumulativeEnergy(ts={"SIJ_Metal_SI_UHP_J12_KumulativnaDelovnaEnergijaPrejeta": "+"}, messages=messages)
        #current_energy_uhp_consumed, uhp_ts = PowerCalcRavne.cumulativeEnergy(ts={"SIJ_Metal_SI_UHP_J12_KumulativnaDelovnaEnergijaOddana": "-"}, messages=messages)
        #current_energy_uhp = current_energy_uhp - current_energy_uhp_consumed
        if timestamp and power_current_interval[0] > 0:
            averageWPEndInterval = PowerCalcRavne.processAveragePowerEndInterval(energy, timestamp, interval_end, power_current_interval[0])
        if previous_time:
            power = PowerCalcRavne.processCurrentWorkingPower(ts=ts, messages=messages)
            if power > 0:
                averageWPInterval, currentTrendEndInterval = PowerCalcRavne.processBillingPowerTrendEndoFfInterval(timestamp, interval_end, energy)

                pres, excess, endPower, uhp_power = PowerCalcRavne.processRavneCurrentWorkingPowerWithoutUHP(power, timestamp, interval_end, energy,
                                                                                current_energy_uhp, previous_uhp_energy[0], previous_uhp_timestamp[0], uhp_timestamp,
                                                                                power_current_interval[0], data_missing_alarm, excess                                                                               )
        return (averageWPEndInterval, currentTrendEndInterval, averageWPInterval, pres, excess, endPower, uhp_power)

    @staticmethod
    def processCurrentWorkingPower(ts: dict, messages: list):
        '''Trenutna delovna moč'''
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        timeseries_dict = {}
        powerSum = 0
        for timeseries_name, timestamp, energy_value in list(messages):
            if timeseries_name in ts:
                time_real = datetime.strptime(timestamp, time_format)

                if timeseries_name not in timeseries_dict:
                    timeseries_dict[timeseries_name] = []
                timeseries_dict[timeseries_name].append(
                    (time_real, energy_value))

        for timeseries_name, data in timeseries_dict.items():
            data.sort(key=lambda x: x[0], reverse=True)

            if len(data) < 2:
                continue

            max_time, max_energy = data[0]
            second_max_time, second_max_energy = data[1]

            # Calculate time difference in seconds
            time_diff = (max_time - second_max_time).total_seconds()
            time_diff_hours = time_diff / 3600

            # Calculate energy difference
            energy_diff = (max_energy - second_max_energy) / 1000

            if time_diff > 0:
                power = energy_diff / time_diff_hours
                sign = ts[timeseries_name]
                if sign == "-":
                    power = -power
                powerSum += power

        return powerSum


    @staticmethod
    def processRavneCurrentWorkingPowerWithoutUHP(power, current_time, interval_end, current_energy,
                                                     current_energy_uhp, previous_uhp_energy, previous_uhp_timestamp, uhp_timestamp,
                                                     power_current_interval, data_missing_alarm, excess):
        '''Povprečna delovna do moč do konca intervala'''
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        current_time_real = datetime.strptime(current_time, time_format)

        time_delta_seconds = (
            interval_end - current_time_real.replace(tzinfo=timezone.utc)).total_seconds()

        time_delta_hours = time_delta_seconds / 3600

        if previous_uhp_timestamp != None:
            uhp_curr_power = PowerCalcRavne.calculateUHPPower(previous_uhp_timestamp, uhp_timestamp, current_energy_uhp, previous_uhp_energy)
        else:
            uhp_curr_power = 0
        endEnergy = (power - uhp_curr_power) * time_delta_hours * 1000
        # realEndEnergy = (current_energy - current_energy_demag) + endEnergy
        if endEnergy < 0:
            realEndEnergy = current_energy
        else:
            realEndEnergy = current_energy + endEnergy
        endPower = realEndEnergy * 4 / 1000
        logging.debug(
            "---------------------------------------------------------")
        logging.debug(
            "Processing: processMetalCurrentWorkingPowerWithoutUHP")
        logging.debug(f"time_delta_seconds = {time_delta_seconds}")
        logging.debug(f"time_delta_hours = {time_delta_hours}")
        logging.debug(f"power = {power}")
        logging.debug(f"uhp_curr_power = {uhp_curr_power}")
        logging.debug(f"current_energy = {current_energy}")
        logging.debug(f"endEnergy = {endEnergy}")
        logging.debug(f"realEndEnergy = {realEndEnergy}")
        logging.debug(f"endPower = {endPower}")
        logging.debug(
            "-----------------------END----------------------------------")
        pres = 0
        calc_pres = power_current_interval * 0.95

        if endPower > calc_pres and power_current_interval != None and power_current_interval > 0:
            if data_missing_alarm == 0 or data_missing_alarm == None:
                if excess == 1:
                    pres = 1
                else:
                    excess = 1
        else:
            excess = 0
        return (pres, excess, endPower, uhp_curr_power)


    @staticmethod
    def processBillingPowerTrendEndoFfInterval(current_time, interval_end, current_energy):
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        current_time_real = datetime.strptime(current_time, time_format)
        time_delta_seconds = (
            interval_end - current_time_real.replace(tzinfo=timezone.utc)).total_seconds()
        time_delta_hours = time_delta_seconds / 3600

        start_interval = interval_end - timedelta(minutes=15)
        pw_time_delta_seconds = (current_time_real.replace(tzinfo=timezone.utc) - start_interval).total_seconds()
        pw_time_delta_hours = pw_time_delta_seconds / 3600
        power = current_energy / (pw_time_delta_hours * 1000)

        endEnergy = power * time_delta_hours * 1000
        realEndEnergy = current_energy + endEnergy
        endPower = realEndEnergy * 4 / 1000
        logging.debug(
            "---------------------------------------------------------")
        logging.debug("Processing: processMetalBillingPowerTrendEndoFfInterval")
        logging.debug(f"time_delta_seconds = {time_delta_seconds}")
        logging.debug(f"time_delta_hours = {time_delta_hours}")
        logging.debug(f"power = {power}")
        logging.debug(f"current_energy = {current_energy}")
        logging.debug(f"endEnergy = {endEnergy}")
        logging.debug(f"realEndEnergy = {realEndEnergy}")
        logging.debug(f"endPower = {endPower}")
        logging.debug(
            "-----------------------END----------------------------------")

        return (endPower, power)


    @staticmethod
    def calculateUHPPower(previous_uhp_timestamp, uhp_timestamp, energy, uhp_energy_before):
        # Define your date format
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"

        if not uhp_timestamp:
            return 0
        current_time_real = datetime.strptime(uhp_timestamp, time_format)
        previous_time = datetime.strptime(previous_uhp_timestamp, time_format)

        time_delta_seconds = (current_time_real -
                              previous_time).total_seconds()

        time_delta_hours = time_delta_seconds / 3600

        logging.debug(
            "---------------------------------------------------------")
        logging.debug("Processing: calculateUHPPower")
        logging.debug(f"current_time_real = {current_time_real}")
        logging.debug(f"previous_time = {previous_time}")
        logging.debug(f"delta time in seconds  = {time_delta_seconds}")
        logging.debug(f"time_delta_hours = {time_delta_hours}")
        logging.debug(f"energy = {energy}")
        logging.debug(f"self.energy_before[0] = {uhp_energy_before}")

        if time_delta_hours != 0 and time_delta_seconds >= 2:
            power = (
                (energy - uhp_energy_before) / time_delta_hours) / 1000
            if power < 0:
                power = 0
            logging.debug(f"power = {power}")
            logging.debug(
                "---------------------END------------------------------------")
            return power
        else:
            return 0

    @staticmethod
    def currentEnergy(ts: dict, messages: list) -> tuple[float, datetime]:
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        timeseries_dict = {}
        current_energy = 0
        current_time = None
        second_max_energy = 0
        for timeseries_name, timestamp, energy_value in list(messages):
            if timeseries_name in ts:
                time_real = datetime.strptime(timestamp, time_format)

                if timeseries_name not in timeseries_dict:
                    timeseries_dict[timeseries_name] = []
                timeseries_dict[timeseries_name].append(
                    (time_real, energy_value))

        for timeseries_name, data in timeseries_dict.items():
            data.sort(key=lambda x: x[0], reverse=True)
            if len(data) < 2:
                continue

            current_time, current_energy = data[0]
            second_max_time, second_max_energy = data[1]

        return (current_energy - second_max_energy), current_time

    @staticmethod
    def processAveragePowerEndInterval(energy, current_time, interval_end, power_current_interval):
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        current_time_real = datetime.strptime(current_time, time_format)
        billing_energy = (power_current_interval / 4) * 1000
        logging.debug(
            "---------------------------------------------------------")
        logging.debug("Processing: processMetalAveragePowerEndInterval")
        logging.debug(f"billing_energy = {billing_energy}")
        logging.debug(f"current_energy = {energy}")

        delta_E = billing_energy - energy
        delta_t_seconds = (interval_end - current_time_real.replace(tzinfo=timezone.utc)).total_seconds()
        delta_t_hours = delta_t_seconds / 3600
        logging.debug(f"delta time in seconds  = {delta_t_seconds}")
        logging.debug(f"delta time in hours = {delta_t_hours}")
        avg_power_end_interval = 0
        if delta_t_hours > 0 and delta_t_seconds >= 2:  # Ensure no division by zero
            avg_power_end_interval = (delta_E / delta_t_hours) / 1000  # Power in MW

        logging.debug(f"avg_power_end_interval = {avg_power_end_interval}")
        logging.debug(
            "---------------------------------------------------------")
        return avg_power_end_interval


    @staticmethod
    def cumulativeEnergy(ts: dict, messages: list) -> tuple[float, datetime]:
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        timeseries_dict = {}
        current_energy = 0
        first_energy = 0
        current_time = None
        second_max_energy = 0
        for timeseries_name, timestamp, energy_value in list(messages):
            if timeseries_name in ts:
                time_real = datetime.strptime(timestamp, time_format)

                if timeseries_name not in timeseries_dict:
                    timeseries_dict[timeseries_name] = []
                timeseries_dict[timeseries_name].append(
                    (time_real, energy_value))

        for timeseries_name, data in timeseries_dict.items():
            data.sort(key=lambda x: x[0])
            if len(data) < 2:
                continue

            current_time, current_energy = data[-1]
            first_time, first_energy = data[0]

        return (current_energy - first_energy), current_time

