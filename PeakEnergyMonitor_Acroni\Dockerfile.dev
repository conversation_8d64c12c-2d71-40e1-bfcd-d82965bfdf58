# Use the Python 3.11 slim image based on Debian Bullseye
FROM python:3.13-slim-bullseye

# Set metadata for the image
LABEL maintainer="<PERSON><PERSON><PERSON> <dusan.k<PERSON><PERSON><PERSON>@kolektor.com>"

# Copy pip configuration file
COPY ./pip.ini /etc/pip.conf

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    USER=app \
    ENVIRONMENT=DEV \
    TSAPI_API_URL=https://ts-api.dev.kolektorsetup.si \
    TSAPI_API_KEY=56f3604a794f4911b29d924e2bb8b6b2
    

ENV HOME=/home/<USER>
    PATH=/usr/local/bin:$PATH

# Create a non-root user and set permissions
RUN adduser --uid 2000 --gecos "" --disabled-password $USER \
    && mkdir -p $HOME/scripts \
    && chown -R $USER:$USER $HOME

# Install system dependencies and Azure CLI
RUN apt-get update \
    && apt-get install -y --no-install-recommends gcc libpq-dev wget \
    && wget -q https://aka.ms/InstallAzureCLIDeb -O - | bash \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Switch to non-root user
USER $USER

# Copy requirements and install dependencies
COPY ./app/requirements.txt ./
RUN python -m pip install --upgrade pip \
    && pip install -U --no-cache-dir --no-warn-script-location -r requirements.txt

# Copy application scripts
COPY ./app/scripts $HOME/scripts

# Set the working directory
WORKDIR $HOME/scripts

RUN rm -f $HOME/scripts/acroni-peak-config.json

# Expose the port the app runs on
EXPOSE 8125

# Set default command to run the application
CMD az login --tenant 8541dae1-904b-433f-a318-030780dbfe80 && az aks get-credentials --resource-group setup-flexibility --name flexibility-prod && python peakMonitor.py --port 8125 --host 0.0.0.0
