import logging
import pandas as pd
from ThedoraTS import ThedoraTS
from datetime import datetime, timedelta, timezone
from dateutil.parser import parse
from collections import defaultdict
from ksLib.other.Eventhub import Eventhub
from contextManager import ThreadSafeQueueManager
from powerCalcRavne import PowerCalcRavne
from queue import Empty, Full
import traceback

CONNECTION_STRING = "Endpoint=sb://acroni.servicebus.windows.net/;SharedAccessKeyName=baseline;SharedAccessKey=czdVWD8D+xqVWt6xhEmxIz1LzKLVILyP3+AEhKLxZm4=;EntityPath=acroni_baseline"
TOPIC = "acroni_baseline"

# Configure logging
logger = logging.getLogger(__name__)


ts_power_system = {"SIJ_Systems_SI_Lokacija_KumulativnaDelovnaEnergija": "+"}
ts_power_metal = {"SIJ_Metal_SI_Lokacija_KumulativnaDelovnaEnergija": "+"}
ts_power_rest= {
    "SIJ_Metal_SI_VPP2_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Metal_SI_VPP1_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Metal_SI_UHP_J12_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Metal_SI_UHP_J12_KumulativnaDelovnaEnergijaOddana": "-"
}

ts_power_acroni = {
    "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_ACR_TR3_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaPrejeta": "-",
    "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_ACR_TR3_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaOddana": "+",
    "SIJ_Acroni_SI_Daljnovod_Jesenice_Javornik_KumulativnaDelovnaEnergijaOddana": "+",
    "SIJ_Acroni_SI_Daljnovod_Jesenice_Javornik_KumulativnaDelovnaEnergijaPrejeta": "-"
}

class PeakProcessorRavne:

    def __init__(
        self,
        token,
        timeseries_data_queue_ravne,
        data_lock,
        graph_data,
        ts_api,
        previous_time,
        data_missing_alarm,
        timeseries_data_queue,
        systems_power_current_interval,
        metal_power_current_interval,
        excess,
        previous_uhp_energy,
        previous_uhp_timestamp,
        ravne_cap

    ):

        self.token = token
        self.data_lock = data_lock
        self.graph_data = graph_data
        self.ts_api = ts_api
        self.previous_time = previous_time
        self.data_missing_alarm = data_missing_alarm
        self.valid_timeseries_names_systems = ["SIJ_Systems_SI_Lokacija_KumulativnaDelovnaEnergija"]
        self.valid_timeseries_names_ravne = ["SIJ_Metal_SI_Lokacija_KumulativnaDelovnaEnergija", "SIJ_Metal_SI_UHP_J12_KumulativnaDelovnaEnergijaPrejeta", "SIJ_Metal_SI_UHP_J12_KumulativnaDelovnaEnergijaOddana"]
        self.write_data = 1  # ali piše vse ts 0 - spusti / 1 -piše
        self.queue_data = timeseries_data_queue_ravne
        self.queue_manager_ravne = ThreadSafeQueueManager(timeseries_data_queue_ravne)

        self.acroni_queue_data = timeseries_data_queue
        self.queue_manager_acroni = ThreadSafeQueueManager(timeseries_data_queue)
        self.systems_power_current_interval = systems_power_current_interval
        self.metal_power_current_interval = metal_power_current_interval
        self.excess = excess
        self.previous_uhp_energy = previous_uhp_energy
        self.previous_uhp_timestamp = previous_uhp_timestamp
        self.ravne_cap = ravne_cap
        self.messages = []
        self.messages_acroni = []


    def get_interval_start_time(self, timestamp):

        interval_minutes = 15
        # Convert timestamp to a datetime object if not already
        timestamp = pd.to_datetime(timestamp)
        return timestamp - pd.Timedelta(
            minutes=timestamp.minute % interval_minutes,
            seconds=timestamp.second,
            microseconds=timestamp.microsecond,
        )

    def clear_and_repopulate_queue(self, current_interval_start):
        logger.warning("RAVNE Starting clear_and_repopulate_queue operation")
        temp_list = []
        next_interval_start = current_interval_start

        try:
            start_hour = current_interval_start.replace(minute=0, second=0, microsecond=0)
            next_hour_start = start_hour + timedelta(hours=1)
            logger.warning(f"RAVNE Setting time window: {start_hour} to {next_hour_start}")

            with self.data_lock:
                logger.warning("RAVNE Acquired data_lock for initial state update")
                self.ravne_cap[0] = datetime.now(timezone.utc)

            def fetch_systems_power():
                try:
                    logger.warning("RAVNE Fetching Systems power data")
                    data = self.ts_api.get_timeseries_data(
                        '12d6bdd3-02bc-466b-8504-ea544a7d2956',
                        start_hour,
                        next_hour_start
                    )
                    if data:
                        max_entry = max(
                            data,
                            key=lambda x: datetime.strptime(x['dateTimeTick'], '%Y-%m-%dT%H:%M:%S.%fZ')
                        )
                        logger.warning(f"RAVNE Systems power value retrieved: {max_entry['numericValue']}")
                        return max_entry['numericValue']
                    else:
                        logger.warning("RAVNE No data found for Systems power")
                        return 0
                except Exception as e:
                    logger.warning(f"RAVNE Error fetching Systems power data: {str(e)}")
                    return 0

            def fetch_metal_power():
                try:
                    logger.warning("RAVNE Fetching Metal power data")
                    data = self.ts_api.get_timeseries_data(
                        'b3b1b927-346e-434b-ae68-de194996bf58',
                        start_hour,
                        next_hour_start
                    )
                    if data:
                        max_entry = max(
                            data,
                            key=lambda x: datetime.strptime(x['dateTimeTick'], '%Y-%m-%dT%H:%M:%S.%fZ')
                        )
                        logger.warning(f"RAVNE Metal power value retrieved: {max_entry['numericValue']}")
                        return max_entry['numericValue']
                    else:
                        logger.warning("RAVNE No data found for Metal power")
                        return 0
                except Exception as e:
                    logger.warning(f"RAVNE Error fetching Metal power data: {str(e)}")
                    return 0

            with self.data_lock:
                logger.warning("RAVNE Acquired data_lock for power updates")
                self.systems_power_current_interval[0] = fetch_systems_power()
                self.metal_power_current_interval[0] = fetch_metal_power()
                self.messages = []
                logger.warning("RAVNE Updated power values and reset messages")

            with self.data_lock:
                logger.warning("RAVNE Acquired data_lock for queue processing")
                while not self.queue_data.empty():
                    try:
                        data = self.queue_data.get_nowait()
                        if data is not None:
                            temp_list.append(data)
                            #logger.warning(f"RAVNE Retrieved queue item: {data}")
                    except Empty:
                        logger.warning("RAVNE Queue is now empty")
                        break
                    except Exception as e:
                        logger.warning(f"RAVNE Error getting data from queue: {str(e)}")
                        continue

            valid_items = []
            logger.warning("RAVNE Starting validation of collected items")
            for item in temp_list:
                try:
                    if not isinstance(item, tuple) or len(item) != 3:
                        logger.warning(f"RAVNE Invalid data format found: {item}")
                        continue

                    timeseries_name, timestamp, value = item
                    timestamp = pd.to_datetime(timestamp)
                    interval_start_time = self.get_interval_start_time(timestamp)

                    if interval_start_time >= current_interval_start:
                        valid_items.append(item)
                        #logger.warning(f"RAVNE Valid item found: {item}")

                except Exception as e:
                    logger.warning(f"RAVNE Error processing item {item}: {str(e)}")
                    continue
            logger.warning(f"RAVNE Validation complete. Valid items: {len(valid_items)}")

            with self.data_lock:
                logger.warning("RAVNE Acquired data_lock for repopulating queue")
                for item in valid_items:
                    try:
                        self.queue_data.put_nowait(item)
                        self.messages.append(item)
                        #logger.warning(f"RAVNE Successfully requeued item: {item}")
                    except Full:
                        logger.warning("RAVNE Queue full during repopulation")
                        break

                try:
                    if not self.queue_data.empty():
                        logger.warning("RAVNE Checking last entry in queue")
                        queue_items = list(self.queue_data.queue)
                        if queue_items:
                            last_entry = queue_items[-1]
                            next_interval_start = pd.to_datetime(last_entry[1])
                            #logger.warning(f"RAVNE Next interval start set to: {next_interval_start}")
                except Exception as e:
                    logger.warning(f"RAVNE Failed to determine next interval start: {str(e)}")
                    next_interval_start = current_interval_start

                next_interval_end = next_interval_start + timedelta(minutes=15)
                #logger.warning(f"RAVNE Next interval end set to: {next_interval_end}")

        except Exception as e:
            logger.warning(f"RAVNE Critical error in clear_and_repopulate: {str(e)}")
            self._reset_state()

    def _reset_state(self):
        logger.warning("RAVNE Starting state reset")
        try:
            with self.data_lock:
                logger.warning("RAVNE Acquired data_lock for state reset")
                while not self.queue_data.empty():
                    try:
                        self.queue_data.get_nowait()
                    except Empty:
                        break
                logger.warning("RAVNE Cleared queue_data")

                self.messages = []
                self.previous_uhp_energy[0] = None
                self.previous_uhp_timestamp[0] = None
                self.data_missing_alarm[1] = 0
                self.excess[1] = 0
                self.ravne_cap[0] = datetime.now(timezone.utc)
                logger.warning("RAVNE Reset all state variables")

            logger.warning("RAVNE State reset completed successfully")
        except Exception as e:
            logger.warning(f"RAVNE Failed to reset state: {str(e)}")


    def process(self):
        try:
            interval_start_time_max = datetime.now(timezone.utc)
            timestamp = None
            processed_timeseries = set()
            interval_end = None
            value_sum_uhp = 0
            uhp_timestamp = None
            value_sum_metal = 0
            value_sum_systems = 0
            current_interval = None
            messages = []
            messages = self.queue_manager_ravne.get_messages(preserve=True)
            self.messages = messages

            metrics = self.queue_manager_ravne.get_metrics()
            logger.warning(f"Queue metrics: {metrics}")

            messages_acroni = []
            messages_acroni = self.queue_manager_acroni.get_messages(preserve=True)
            self.messages_acroni = messages_acroni

            metrics = self.queue_manager_acroni.get_metrics()
            logger.warning(f"Queue metrics: {metrics}")

            check_results = self.monitor_timeseries_data(self.valid_timeseries_names_systems, 'Systems')
            check_results = self.monitor_timeseries_data(self.valid_timeseries_names_ravne, 'Metal')

            for message in self.messages:
                timeseries_name, time, numeric_value = message
                current_interval_start = self.get_interval_start_time(time)
                message_interval = self.get_interval_start_time(time)
                if current_interval is None:
                    current_interval = message_interval
                elif message_interval != current_interval or self.is_data_stale():
                    self.clear_and_repopulate_queue(interval_start_time_max)
                    value_sum_uhp = 0
                    value_sum_systems = 0
                    value_sum_metal = 0
                    processed_timeseries.clear()
                    current_interval = message_interval
                    continue

                if ((timeseries_name in ts_power_rest) or (timeseries_name in ts_power_system) or (timeseries_name in ts_power_metal)) \
                    and timeseries_name not in processed_timeseries:
                    min_record, max_record = self.find_min_max_time(timeseries_name, current_interval_start)

                    if min_record and max_record:
                        _, min_time, min_value = min_record
                        _, max_time, max_value = max_record
                        interval_start_time_min = self.get_interval_start_time(min_time)
                        interval_start_time_max = self.get_interval_start_time(max_time)
                        interval_end = interval_start_time_min + \
                            timedelta(minutes=15)

                        # Compute adjusted value based on the operation
                        adjusted_value = max_value - min_value
                        if timeseries_name == "SIJ_Metal_SI_UHP_J12_KumulativnaDelovnaEnergijaPrejeta":
                            value_sum_uhp += adjusted_value
                            uhp_timestamp = max_time
                        elif timeseries_name == "SIJ_Metal_SI_UHP_J12_KumulativnaDelovnaEnergijaOddana":
                            value_sum_uhp -= adjusted_value

                        if timeseries_name == "SIJ_Systems_SI_Lokacija_KumulativnaDelovnaEnergija":
                            value_sum_systems += adjusted_value
                        if timeseries_name == "SIJ_Metal_SI_Lokacija_KumulativnaDelovnaEnergija":
                            value_sum_metal += adjusted_value
                            timestamp = max_time
                        # logger.info("Processing timeseries: %s, adjusted value: %f", timeseries_name, adjusted_value)

                        processed_timeseries.add(timeseries_name)


            powerMetal = PowerCalcRavne.processCurrentWorkingPower(ts_power_metal, self.messages)
            powerSystems = PowerCalcRavne.processCurrentWorkingPower(ts_power_system, self.messages)
            powerRest = PowerCalcRavne.processCurrentWorkingPower(ts_power_rest, self.messages_acroni)
            powerAcroni = PowerCalcRavne.processCurrentWorkingPower(ts_power_acroni, self.messages_acroni)
            powerSIJ = powerMetal + powerSystems + powerAcroni
            now_utc = datetime.now(timezone.utc)
            time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
            current_time = now_utc.strftime(time_format)

            averageWPEndIntervalSystems, currentTrendEndIntervalSystems, averageWPIntervalSystems, presSystems, self.excess[2] = \
                PowerCalcRavne.peakSystems(ts_power_system, self.messages, timestamp, self.previous_time[2],  interval_end,
                                           self.systems_power_current_interval, self.data_missing_alarm[2], self.excess[2])


            averageWPEndIntervalMetal, currentTrendEndIntervalMetal, averageWPIntervalMetal, presMetal, self.excess[1], endPower, uhp_calc = \
                PowerCalcRavne.peakMetal(ts_power_metal, value_sum_uhp, uhp_timestamp, self.messages, timestamp, self.previous_time[1], interval_end,
                                           self.metal_power_current_interval, self.data_missing_alarm[1], self.excess[1],
                                           self.previous_uhp_energy, self.previous_uhp_timestamp)

            self.previous_time[1] = timestamp
            self.previous_time[2] = timestamp
            self.previous_uhp_energy[0] = value_sum_uhp
            self.previous_uhp_timestamp[0] = uhp_timestamp

            if self.write_data == 1:
                payload = [{
                    "timeSeriesName": 'SIJ_Metal_EnergijaTrenutniObrInterval',
                    "timeSeriesValueInputs": [{
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": value_sum_metal
                    }]
                }]
                self.ts_api.save_timeseries_data(payload)
                payload = [{
                    "timeSeriesName": 'SIJ_Systems_EnergijaTrenutniObrInterval',
                    "timeSeriesValueInputs": [{
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": value_sum_systems
                    }]
                }]
                self.ts_api.save_timeseries_data(payload)

                thedora = ThedoraTS(self.token)
                df = pd.DataFrame(
                    {'date_time_value': [current_time], 'numeric_value': [powerMetal]})
                thedora_data = thedora.prepare_data(
                    df, value=0, multiplier=1, unit="MW", timeZone="CET")
                thedora.post_data(thedora_data, 793)

                df = pd.DataFrame(
                    {'date_time_value': [current_time], 'numeric_value': [powerSystems]})
                thedora_data = thedora.prepare_data(
                    df, value=0, multiplier=1, unit="MW", timeZone="CET")
                thedora.post_data(thedora_data, 794)

                payload = [{
                    "timeSeriesName": 'SIJ_Systems_Trenutna_Delovna_Moc_MW',
                    "timeSeriesValueInputs": [{
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": powerSystems
                    }]
                }]
                self.ts_api.save_timeseries_data(payload)

                df = pd.DataFrame(
                    {'date_time_value': [current_time], 'numeric_value': [powerRest]})
                thedora_data = thedora.prepare_data(
                    df, value=0, multiplier=1, unit="MW", timeZone="CET")

                thedora.post_data(thedora_data, 795)
                df = pd.DataFrame(
                    {'date_time_value': [current_time], 'numeric_value': [powerSIJ]})
                thedora_data = thedora.prepare_data(
                    df, value=0, multiplier=1, unit="MW", timeZone="CET")
                thedora.post_data(thedora_data, 796)
                payload = [{
                    "timeSeriesName": 'SIJ_Skupina_SI_TrenutnaDelovnaMoc',
                    "timeSeriesValueInputs": [{
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": powerSIJ
                    }]
                }]
                self.ts_api.save_timeseries_data(payload)
                if uhp_calc is not None:
                    payload = [{
                        "timeSeriesName": 'SIJ_UHP_SI_TrenutnaDelovnaMoc_Izracunana',
                        "timeSeriesValueInputs": [{
                            "dateTimeTick": current_time,
                            "dateTimeValue": current_time,
                            "dateTimeToValue": current_time,
                            "numericValue": uhp_calc
                        }]
                    }]
                    self.ts_api.save_timeseries_data(payload)

                payload = [{
                    "timeSeriesName": 'SIJ_Metal_SI_TrenutnaMocObrInterval_MW',
                    "timeSeriesValueInputs": [{
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": powerMetal
                    }]
                }]
                self.ts_api.save_timeseries_data(payload)

                payload = [{
                    "timeSeriesName": 'SIJ_Metal_SI_OstaliOdjem',
                    "timeSeriesValueInputs": [{
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": powerMetal - powerRest
                    }]
                }]
                self.ts_api.save_timeseries_data(payload)

                payload = [{
                    "timeSeriesName": 'SIJ_Metal_SI_DelovnaMocTrenutniTrendBrezUHPKonecIntervala_MW',
                    "timeSeriesValueInputs": [{
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": endPower
                    }]
                }]
                self.ts_api.save_timeseries_data(payload)

                if averageWPEndIntervalSystems:
                    #thedora = ThedoraTS(self.token)
                    #df = pd.DataFrame(
                    #    {'date_time_value': [current_time], 'numeric_value': [averageWPEndInterval]})
                    #thedora_data = thedora.prepare_data(
                    #    df, value=0, multiplier=1, unit="MW", timeZone="CET")
                    #thedora.post_data(thedora_data, 725)
                    # Prepare payload for the API
                    payload = []
                    time_series = [
                        {
                            "dateTimeTick": current_time,
                            "dateTimeValue": current_time,
                            "dateTimeToValue": current_time,
                            "numericValue": averageWPEndIntervalSystems
                        }
                    ]
                    payload.append({
                        "timeSeriesName": 'SIJ_Systems_SI_PovprecnaDelovnaMocDoKoncaIntervala_MW',
                        "timeSeriesValueInputs": time_series
                    })
                    try:
                        self.ts_api.save_timeseries_data(payload)
                    except Exception as e:
                        # Log the exception with additional details
                        logging.error(f"Error saving timeseries data: {str(e)}")

                payload_array = []
                #thedora = ThedoraTS(self.token)
                #df = pd.DataFrame(
                #    {'date_time_value': [current_time], 'numeric_value': [currentTrendEndInterval]})
                #thedora_data = thedora.prepare_data(
                #    df, value=0, multiplier=1, unit="MW", timeZone="CET")
                #thedora.post_data(thedora_data, 730)
                # Prepare payload for the API
                payload = []
                time_series = [
                    {
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": currentTrendEndIntervalSystems
                    }
                ]
                payload.append({
                    "timeSeriesName": 'SIJ_Systems_SI_DelovnaMocTrenutniTrendKonecIntervala_MW',
                    "timeSeriesValueInputs": time_series
                })
                payload_array.append(payload)
                #df = pd.DataFrame(
                #    {'date_time_value': [current_time], 'numeric_value': [averageWPInterval]})
                #thedora_data = thedora.prepare_data(
                #    df, value=0, multiplier=1, unit="MW", timeZone="CET")
                #thedora.post_data(thedora_data, 738)
                # Prepare payload for the API
                payload = []
                time_series = [
                    {
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": averageWPIntervalSystems
                    }
                ]
                payload.append({
                    "timeSeriesName": 'SIJ_Systems_SI_PovprecnaDelovnaMocInterval',
                    "timeSeriesValueInputs": time_series
                })
                payload_array.append(payload)
                try:
                    for payload in payload_array:
                        self.ts_api.save_timeseries_data(payload)
                        #if payload[0]["timeSeriesName"] != "SIJ_Acroni_SI_PovprecnaDelovnaMocInterval":
                            #result = Eventhub.send_payload_to_topic(CONNECTION_STRING, TOPIC, payload)
                except Exception as e:
                    # Log the exception with additional details
                    logging.error(f"Error saving timeseries data: {str(e)}")
#---------------------------------------RAVNE-----------------------------------------------------------------------------------
                if averageWPEndIntervalMetal:
                    #thedora = ThedoraTS(self.token)
                    #df = pd.DataFrame(
                    #    {'date_time_value': [current_time], 'numeric_value': [averageWPEndInterval]})
                    #thedora_data = thedora.prepare_data(
                    #    df, value=0, multiplier=1, unit="MW", timeZone="CET")
                    #thedora.post_data(thedora_data, 725)
                    # Prepare payload for the API
                    payload = []
                    time_series = [
                        {
                            "dateTimeTick": current_time,
                            "dateTimeValue": current_time,
                            "dateTimeToValue": current_time,
                            "numericValue": averageWPEndIntervalMetal
                        }
                    ]
                    payload.append({
                        "timeSeriesName": 'SIJ_Metal_SI_PovprecnaDelovnaMocDoKoncaIntervala_MW',
                        "timeSeriesValueInputs": time_series
                    })
                    try:
                        self.ts_api.save_timeseries_data(payload)
                    except Exception as e:
                        # Log the exception with additional details
                        logging.error(f"Error saving timeseries data: {str(e)}")

                payload_array = []
                #thedora = ThedoraTS(self.token)
                #df = pd.DataFrame(
                #    {'date_time_value': [current_time], 'numeric_value': [currentTrendEndInterval]})
                #thedora_data = thedora.prepare_data(
                #    df, value=0, multiplier=1, unit="MW", timeZone="CET")
                #thedora.post_data(thedora_data, 730)
                # Prepare payload for the API
                payload = []
                time_series = [
                    {
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": currentTrendEndIntervalMetal
                    }
                ]
                payload.append({
                    "timeSeriesName": 'SIJ_Metal_SI_DelovnaMocTrenutniTrendKonecIntervala_MW',
                    "timeSeriesValueInputs": time_series
                })
                payload_array.append(payload)
                #df = pd.DataFrame(
                #    {'date_time_value': [current_time], 'numeric_value': [averageWPInterval]})
                #thedora_data = thedora.prepare_data(
                #    df, value=0, multiplier=1, unit="MW", timeZone="CET")
                #thedora.post_data(thedora_data, 738)
                # Prepare payload for the API
                payload = []
                time_series = [
                    {
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": averageWPIntervalMetal
                    }
                ]
                payload.append({
                    "timeSeriesName": 'SIJ_Metal_SI_PovprecnaDelovnaMocInterval',
                    "timeSeriesValueInputs": time_series
                })
                payload_array.append(payload)
                time_series = [
                    {
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": presMetal
                    }
                ]
                payload.append({
                    "timeSeriesName": 'SIJ_Metal_SI_PresezenaObracunskaMocAlarm',
                    "timeSeriesValueInputs": time_series
                })
                payload_array.append(payload)
                try:
                    for payload in payload_array:
                        self.ts_api.save_timeseries_data(payload)
                        #if payload[0]["timeSeriesName"] != "SIJ_Acroni_SI_PovprecnaDelovnaMocInterval":
                            #result = Eventhub.send_payload_to_topic(CONNECTION_STRING, TOPIC, payload)
                except Exception as e:
                    # Log the exception with additional details
                    logging.error(f"Error saving timeseries data: {str(e)}")

        except Exception as e:
            # Log the exception with additional details
            logging.error(f"Major error in processing Metal&Systems: {str(e)}")
            traceback.print_exc()
            return

    def find_min_max_time(self, timeseries_name, interval_start_time):
        min_record = None
        max_record = None

        # Single pass through messages
        for data in self.messages:
            current_name, current_time, current_value = data

            if current_name == timeseries_name:
                message_interval_start = self.get_interval_start_time(current_time)

                if message_interval_start == interval_start_time:
                    if min_record is None or current_time < min_record[1]:
                        min_record = data

                    if max_record is None or current_time > max_record[1]:
                        max_record = data

        return min_record, max_record


    def monitor_timeseries_data(self, valid_timeseries_names, location):
        if location == 'Metal':
            data_loss_alarm_ts = 'SIJ_Metal_SI_IzpadPodatkovAlarm'
            data_loss_warning_ts = 'SIJ_Metal_SI_IzpadPodatkovOpozorilo'
        else:
            data_loss_alarm_ts = 'SIJ_Systems_SI_IzpadPodatkovAlarm'
            data_loss_warning_ts = 'SIJ_Systems_SI_IzpadPodatkovOpozorilo'

        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        if location == 'Metal':
            self.data_missing_alarm[1] = 0
        else:
            self.data_missing_alarm[2] = 0

        last_update_times = {}
        before_last_update_times = {}
        temp_queue = []
        messages_all = self.messages
        # Dictionary to keep track of the two highest timestamps for each timeseries
        timeseries_max_times = defaultdict(lambda: (None, None))  # (max_time, second_max_time)

        # Process all entries in the queue
        for entry in messages_all:
            try:
                timeseries_name, timestamp, value = entry

                # Store the entry in the temporary list
                temp_queue.append(entry)

                timestamp_real = datetime.strptime(timestamp, time_format).replace(tzinfo=timezone.utc)

                if timeseries_name in valid_timeseries_names:
                    current_max, current_second_max = timeseries_max_times[timeseries_name]

                    # Update the max and second max times for this timeseries
                    if current_max is None:
                        timeseries_max_times[timeseries_name] = (
                            timestamp_real, None)
                    elif timestamp_real > current_max:
                        timeseries_max_times[timeseries_name] = (
                            timestamp_real, current_max)
                    elif current_second_max is None or timestamp_real > current_second_max:
                        timeseries_max_times[timeseries_name] = (
                            current_max, timestamp_real)

            except Exception as e:
                logging.error(f"Ravne Error processing queue entry: {e}")

        # After processing all entries, populate the last_update_times and before_last_update_times
        for timeseries_name, (max_time, second_max_time) in timeseries_max_times.items():
            if max_time is not None:
                last_update_times[timeseries_name] = max_time
            if second_max_time is not None:
                before_last_update_times[timeseries_name] = second_max_time

        # If there's no valid time information, return an empty result
        if not timeseries_max_times:
            current_utc = datetime.now(timezone.utc)
            ct = current_utc.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            payloadNone = []
            time_series = [
                {
                    "dateTimeTick": ct,
                    "dateTimeValue": ct,
                    "dateTimeToValue": ct,
                    "numericValue": 1
                }
            ]
            payloadNone.append({
                "timeSeriesName": data_loss_alarm_ts,
                "timeSeriesValueInputs": time_series
            })
            try:
                if self.write_data == 1:
                    self.ts_api.save_timeseries_data(payloadNone)
                    result = Eventhub.send_payload_to_topic( CONNECTION_STRING, TOPIC, payloadNone)
                if location == 'Metal':
                    self.data_missing_alarm[1] = 1
                else:
                    self.data_missing_alarm[2] = 1
            except Exception as e:
                logging.error(f"Error saving timeseries data: {str(e)}")
            return []

        results = []
        # Move this outside the loop since it's used multiple times
        current_utc = datetime.now(timezone.utc)

        # Check each timeseries for missing data
        for ts_name in valid_timeseries_names:
            last_update = last_update_times.get(ts_name, None)
            before_last_update = before_last_update_times.get(ts_name, None)

            warning = 0
            error = 0
            missing = 0

            if last_update and before_last_update:
                time_between = (last_update - before_last_update).total_seconds()
                time_since_last_update = (current_utc - last_update).total_seconds()

                warning = 1 if time_since_last_update >= 30 or time_between > 15 else 0
                error = 1 if time_between >= 30 or time_since_last_update >= 60 else 0

                if warning == 1:
                    logging.error(f"{location} Timeseries has no data for 20 seconds  = {ts_name}")
                    logging.error(f"{location} Time since last update: %s, Current UTC: %s, Last Update: %s, Last Update[-1]: %s",
                                  time_since_last_update, current_utc, last_update, before_last_update)
                if error == 1:
                    if location == 'Metal':
                        self.data_missing_alarm[1] = 1
                    else:
                        self.data_missing_alarm[2] = 1
                    logging.error(f"{location} Timeseries has no data for 60 seconds  = {ts_name}")
                    logging.error(f"{location} Time since last update: %s, Current UTC: %s, Last Update: %s",
                                  time_since_last_update, current_utc, last_update)
            else:
                # For timeseries with no data
                warning = 1
                error = 1
                missing = 1
                self.data_missing_alarm[0] = 1
                logging.error(f"{location} Timeseries is missing  = {ts_name}")

            # Add result for this timeseries
            results.append({
                "timeseries_name": ts_name,
                "warning": warning,
                "error": error,
                "missing": missing
            })

        # Prepare payload for all results
        ct = current_utc.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        err = any(result["warning"] == 1 or result["error"] == 1 or result["missing"] == 1 for result in results)

        try:
            if self.write_data == 1:
                # Only send to Eventhub if any timeseries has an error
                if any(r["error"] == 1 for r in results):
                    payload = [{
                        "timeSeriesName": data_loss_alarm_ts,
                        "timeSeriesValueInputs": [{
                            "dateTimeTick": ct,
                            "dateTimeValue": ct,
                            "dateTimeToValue": ct,
                            "numericValue": 1
                        }]
                    }]
                    self.ts_api.save_timeseries_data(payload)
                    eventhub_result = Eventhub.send_payload_to_topic(CONNECTION_STRING, TOPIC, payload)
                if any(r["warning"] == 1 for r in results):
                    payload = [{
                        "timeSeriesName": data_loss_warning_ts,
                        "timeSeriesValueInputs": [{
                            "dateTimeTick": ct,
                            "dateTimeValue": ct,
                            "dateTimeToValue": ct,
                            "numericValue": 1
                        }]
                    }]
                    self.ts_api.save_timeseries_data(payload)
                if any(r["warning"] == 0 for r in results) and any(r["error"] == 0 for r in results):
                    payload = [{
                        "timeSeriesName": data_loss_alarm_ts,
                        "timeSeriesValueInputs": [{
                            "dateTimeTick": ct,
                            "dateTimeValue": ct,
                            "dateTimeToValue": ct,
                            "numericValue": 0
                        }]
                    }]
                    self.ts_api.save_timeseries_data(payload)
                    eventhub_result = Eventhub.send_payload_to_topic(CONNECTION_STRING, TOPIC, payload)
                    payload = [{
                        "timeSeriesName": data_loss_warning_ts,
                        "timeSeriesValueInputs": [{
                            "dateTimeTick": ct,
                            "dateTimeValue": ct,
                            "dateTimeToValue": ct,
                            "numericValue": 0
                        }]
                    }]
                    self.ts_api.save_timeseries_data(payload)


        except Exception as e:
            logging.error(f"Error saving timeseries data for Ravne: {
                          str(e)} + payload: {payload}")

        return results


    def is_data_stale(self):
        if not self.messages:
            return False

        current_time = datetime.now(timezone.utc)
        latest_message_time = max(msg[1] for msg in self.messages)

        # Make sure latest_message_time is a datetime object with timezone
        if isinstance(latest_message_time, str):
            latest_message_time = datetime.strptime(latest_message_time, '%Y-%m-%dT%H:%M:%S.%fZ')

        # Make latest_message_time timezone aware
        if latest_message_time.tzinfo is None:
            latest_message_time = latest_message_time.replace(tzinfo=timezone.utc)

        try:
            time_difference = (current_time - latest_message_time).total_seconds() / 60  # in minutes
        except Exception as e:
            logging.error(f"Error calculating time difference: {str(e)}")
            return False

        return time_difference > 2