apiVersion: apps/v1
kind: Deployment
metadata:
  name: ravne-peak
  namespace: fsi
spec:
  replicas: 1  # Adjust as needed
  selector:
    matchLabels:
      app: ravne-peak
  template:
    metadata:
      labels:
        app: ravne-peak
    spec:
      containers:
      - name: ravne-peak
        image: kolektorsetup.azurecr.io/fsi/acroni-peak:acroni-peak-0.1.1
        imagePullPolicy: Always       
        volumeMounts:
          - name: python-environment-secret
            readOnly: true
            mountPath: "/etc/.envSecret"
          - name: python-environment
            mountPath: "/etc/.env"
            readOnly: true 
        ports:
          - containerPort: 8125        
        resources:
          limits:
            cpu: "0.5"  # Set the CPU limit (adjust as needed)
            memory: "512Mi"  # Set the memory limit (adjust as needed)
          requests:
            cpu: "0.1"  # Set the CPU request (adjust as needed)
            memory: "256Mi"  # Set the memory request (adjust as needed)
        # ... other container configurations ...
        livenessProbe:
          httpGet:
            path: /health
            port: 8125
          initialDelaySeconds: 90
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8125
          initialDelaySeconds: 90
          periodSeconds: 5
        env:
          - name: RUN_PROCESS
            value: "RAVNE"
      imagePullSecrets:
      - name: bofit-secret
      volumes:
        - name: python-environment-secret
          secret:
            secretName: python-environment-secret
        - name: python-environment
          configMap:
            name: python-environment
        - name: bofit-secret
          secret:
            secretName: bofit-secret