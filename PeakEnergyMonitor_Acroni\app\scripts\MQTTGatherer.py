import logging
import json
import base64

logger = logging.getLogger(__name__)

class MQTTGatherer:

    def __init__(self, timeseries_data_queue, data_lock):
        """
        Initializes the MQTTGatherer class.

        Parameters:
            timeseries_data_queue: A thread-safe queue to store timeseries data.
            data_lock (object): A lock for synchronizing access to the queue.
            ts_api: The instance of the TsAPI class for calling API functions.
        """
        self.timeseries_data_queue = timeseries_data_queue
        self.data_lock = data_lock

    # Method to prepare payload for saving to the time series database
    def gatherTS(self, data):
        """
        Manipulates incoming data and stores it as time series queue.

        Args:
            data (dict): The incoming data to be manipulated.
        """
        try:
            petrol = 0
            topic = data['subject']
            if topic == 'acroni-measurements-mqtt/zds/proizvodnja':
                timeseries_name = 'SIJ_Acroni_SI_ZDS_Jesenice_TrenutnaMocDelovnaOddana'
            elif topic == 'acroni-measurements-mqtt/zds/odjem':
                timeseries_name = 'SIJ_Acroni_SI_ZDS_Jesenice_TrenutnaMocDelovnaPrejeta'
            elif topic == 'acroni-measurements-mqtt/Metal':
                timeseries_name = 'SIJ_Metal_SI_Lokacija_KumulativnaDelovnaEnergija'
                petrol = 1
            elif topic == 'acroni-measurements-mqtt/RavneSystem':
                timeseries_name = 'SIJ_Systems_SI_Lokacija_KumulativnaDelovnaEnergija'
                petrol = 1
            else:
                return

            encoded_data = base64.b64decode(data['data_base64'])
            json_data = json.loads(encoded_data)
            if petrol == 0:
                message = json_data['message']['data'][0]
            else:
                message = json_data['message']['data']
            time = message['time']
            numericValue = message['value']

            with self.data_lock:
                timeseries_data = (timeseries_name, time, numericValue)
                self.timeseries_data_queue.put(timeseries_data)

        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON data: {e}")
            logger.error(f"Data is : ' {data}")
            return None

        except Exception as e:
            logger.error(f"Error in processing data: {e}")
            logger.error(f"Data is : ' {data}")
            return None

