import json
import requests
import logging

# Configure logging
logger = logging.getLogger(__name__)

class ThedoraLogin:
    """
    A class used to represent the login process for Thedora.
    
    Attributes
    ----------
    url : str
        The URL for the login API endpoint
    user_name : str
        The username for authentication
    password : str
        The password for authentication
    client_id : str
        The client ID for the OAuth2 authentication
    client_secret : str
        The client secret for the OAuth2 authentication
    """
    
    def __init__(self, url, user_name, password, client_id, client_secret):
        """
        Initializes the ThedoraLogin class with the necessary credentials and URL.

        Parameters
        ----------
        url : str
            The URL for the login API endpoint
        user_name : str
            The username for authentication
        password : str
            The password for authentication
        client_id : str
            The client ID for the OAuth2 authentication
        client_secret : str
            The client secret for the OAuth2 authentication
        """
        self.url = url
        self.user_name = user_name
        self.password = password
        self.client_id = client_id
        self.client_secret = client_secret
        logger.info("ThedoraLogin instance created with URL: %s", url)
        
    def login(self):
        """
        Sends a POST request to the login API endpoint to authenticate the user
        and retrieve an access token.

        Returns
        -------
        str
            The access token if the login is successful, None otherwise.
        """
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        payload = {
            "grant_type": "password",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "username": self.user_name,
            "password": self.password
        }

        try:
            res = requests.post(url=self.url, data=payload, headers=headers)
            res.raise_for_status()  # Raises HTTPError for bad responses (4xx or 5xx)
        except requests.exceptions.HTTPError as http_err:
            logger.error("HTTP error occurred: %s", http_err)
            return None
        except requests.exceptions.RequestException as err:
            logger.error("Error occurred: %s", err)
            return None

        try:
            token = res.json()
            #logger.info("Login successful for user: %s", self.user_name)
            return token.get('access_token')
        except json.JSONDecodeError as json_err:
            logger.error("JSON decode error: %s", json_err)
            return None
