import logging
import pandas as pd
import traceback
from ThedoraTS import ThedoraTS
from datetime import datetime, timedelta, timezone
from dateutil.parser import parse
from collections import defaultdict
from ksLib.other.Eventhub import Eventhub
from contextManager import ThreadSafeQueueManager
from queue import Empty, Full

CONNECTION_STRING = "Endpoint=sb://acroni.servicebus.windows.net/;SharedAccessKeyName=baseline;SharedAccessKey=czdVWD8D+xqVWt6xhEmxIz1LzKLVILyP3+AEhKLxZm4=;EntityPath=acroni_baseline"
TOPIC = "acroni_baseline"

# Configure logging
logger = logging.getLogger(__name__)


# A dictionary mapping time series names to their respective operations
time_series_formula = {
    "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_ACR_TR3_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaPrejeta": "-",
    "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_ACR_TR3_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaOddana": "+",
    "SIJ_Acroni_Zunanji_odjemalci_VozniRedSIJ": "-",
    "SIJ_Acroni_SI_DEMAG_KumulativnaDelovnaEnergijaOddana": "",
    "SIJ_Acroni_SI_DEMAG_KumulativnaDelovnaEnergijaPrejeta": ""

}

time_series_power = {
    "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_ACR_TR3_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaPrejeta": "-",
    "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_ACR_TR3_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaOddana": "+",
    "SIJ_Acroni_Zunanji_odjemalci_VozniRedSIJ": "-",
}

whole_power = {
    "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_ACR_TR3_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaPrejeta": "-",
    "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_ACR_TR3_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaOddana": "+",
    "SIJ_Acroni_SI_Daljnovod_Jesenice_Javornik_KumulativnaDelovnaEnergijaOddana": "+",
    "SIJ_Acroni_SI_Daljnovod_Jesenice_Javornik_KumulativnaDelovnaEnergijaPrejeta": "-"
}

rest_power = {
    "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaPrejeta": "+",
    "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaPrejeta": "-",
    "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaOddana": "-",
    "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaOddana": "+",
    "SIJ_Acroni_SI_Daljnovod_Jesenice_Javornik_KumulativnaDelovnaEnergijaOddana": "+",
    "SIJ_Acroni_SI_Daljnovod_Jesenice_Javornik_KumulativnaDelovnaEnergijaPrejeta": "-"
}


class PeakProcessor:

    def __init__(
        self,
        token,
        timeseries_data_queue,
        baseline_data_queue,
        data_lock,
        graph_data,
        ts_api,
        acroni_power_current_interval,
        energy_before,
        previous_time,
        power_before,
        demag_energy_before,
        previous_demag_time,
        data_missing_alarm,
        excess,
        acroni_cap
    ):

        logger.warning("Starting PeakProcessor initialization")
        self.token = token
        self.data_lock = data_lock
        self.graph_data = graph_data
        self.ts_api = ts_api
        self.baseline_data_queue = baseline_data_queue
        self.acroni_power_current_interval = acroni_power_current_interval
        self.energy_before = energy_before
        self.previous_time = previous_time
        self.power_before = power_before
        self.demag_energy_before = demag_energy_before
        self.previous_demag_time = previous_demag_time
        self.data_missing_alarm = data_missing_alarm
        self.valid_timeseries_names = [
            "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Acroni_SI_ACR_TR3_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaPrejeta",
            "SIJ_Acroni_SI_ACR_TR1_KumulativnaDelovnaEnergijaOddana",
            "SIJ_Acroni_SI_ACR_TR2_KumulativnaDelovnaEnergijaOddana",
            "SIJ_Acroni_SI_ACR_TR3_KumulativnaDelovnaEnergijaOddana",
            "SIJ_Acroni_SI_BHEE_D003_KumulativnaDelovnaEnergijaOddana",
            # "SIJ_Acroni_Zunanji_odjemalci_VozniRedSIJ",
            "SIJ_Acroni_SI_DEMAG_KumulativnaDelovnaEnergijaOddana",
            "SIJ_Acroni_SI_DEMAG_KumulativnaDelovnaEnergijaPrejeta"
        ]
        self.write_data = 1  # ali piše vse ts 0 - spusti / 1 -piše
        self.queue_data = timeseries_data_queue
        self.queue_manager = ThreadSafeQueueManager(timeseries_data_queue)
        self.excess = excess
        self.acroni_cap = acroni_cap
        self.messages = []

    def get_interval_start_time(self, timestamp):

        interval_minutes = 15
        timestamp = pd.to_datetime(timestamp)
        return timestamp - pd.Timedelta(
            minutes=timestamp.minute % interval_minutes,
            seconds=timestamp.second,
            microseconds=timestamp.microsecond,
        )

    def clear_and_repopulate_queue(self, current_interval_start):
        logger.warning("ACRONI Starting clear_and_repopulate_queue operation")
        temp_list = []
        total_energy = 0
        next_interval_start = current_interval_start

        try:
            with self.data_lock:
                logger.warning("ACRONI Acquired data_lock for initial state reset")
                self.messages = []
                self.energy_before[0] = None
                self.previous_time[0] = None
                self.data_missing_alarm[0] = 0
                self.excess[0] = 0
                self.acroni_cap[0] = datetime.now(timezone.utc)
                logger.warning("ACRONI State variables reset completed")

                while not self.queue_data.empty():
                    try:
                        #logger.warning("ACRONI Attempting to get data from queue_data")
                        data = self.queue_data.get_nowait()
                        if data is not None:
                            temp_list.append(data)
                            #logger.warning(f"ACRONI Successfully retrieved and stored data: {data}")
                    except Empty:
                        logger.warning("ACRONI queue_data is now empty")
                        break
                    except Exception as e:
                        logger.warning(f"ACRONI Failed to get data from queue_data: {str(e)}")
                        continue
                logger.warning(f"ACRONI Finished initial data collection. Items collected: {len(temp_list)}")

            valid_items = []
            logger.warning("ACRONI Starting validation of collected items")
            for item in temp_list:
                try:
                    if not isinstance(item, tuple) or len(item) != 3:
                        logger.warning(f"ACRONI Invalid data format found: {item}")
                        continue

                    timeseries_name, timestamp, value = item
                    timestamp = pd.to_datetime(timestamp)
                    interval_start_time = self.get_interval_start_time(timestamp)

                    if interval_start_time >= current_interval_start:
                        valid_items.append(item)
                        #logger.warning(f"ACRONI Valid item found: {item}")

                except Exception as e:
                    logger.warning(f"ACRONI Error processing item {item}: {str(e)}")
                    continue
            logger.warning(f"ACRONI Validation complete. Valid items: {len(valid_items)}")

            with self.data_lock:
                logger.warning("ACRONI Acquired data_lock for repopulating queue")
                for item in valid_items:
                    try:
                        self.queue_data.put_nowait(item)
                        self.messages.append(item)
                        #logger.warning(f"ACRONI Successfully requeued item: {item}")
                    except Full:
                        logger.warning("ACRONI Queue full during repopulation")
                        break

                try:
                    if not self.queue_data.empty():
                        logger.warning("ACRONI Checking last entry in queue")
                        queue_items = list(self.queue_data.queue)
                        if queue_items:
                            last_entry = queue_items[-1]
                            next_interval_start = pd.to_datetime(last_entry[1])
                            logger.warning(f"ACRONI Next interval start set to: {next_interval_start}")
                except Exception as e:
                    logger.warning(f"ACRONI Failed to determine next interval start: {str(e)}")
                    next_interval_start = current_interval_start

            next_interval_end = next_interval_start + timedelta(minutes=15)
            logger.warning(f"ACRONI Next interval end set to: {next_interval_end}")

            baseline_entries = []
            with self.data_lock:
                logger.warning("ACRONI Acquired data_lock for baseline data processing")
                while not self.baseline_data_queue.empty():
                    try:
                        entry = self.baseline_data_queue.get_nowait()
                        if entry is not None:
                            baseline_entries.append(entry)
                            #logger.warning(f"ACRONI Retrieved baseline entry: {entry}")
                    except Empty:
                        logger.warning("ACRONI Baseline queue is now empty")
                        break
                    except Exception as e:
                        logger.warning(f"ACRONI Error getting baseline data: {str(e)}")
                        continue
                logger.warning(f"ACRONI Retrieved {len(baseline_entries)} baseline entries")

            logger.warning("ACRONI Processing baseline entries")
            for entry in baseline_entries:
                try:
                    if not isinstance(entry, tuple) or len(entry) != 3:
                        logger.warning(f"ACRONI Invalid baseline entry format: {entry}")
                        continue

                    entry_ts_name, entry_time_str, entry_value = entry
                    entry_time = datetime.strptime(
                        entry_time_str, "%Y-%m-%dT%H:%M:%S.%fZ"
                    ).replace(tzinfo=timezone.utc)
                    #logger.warning(f"ACRONI Processing entry for time: {entry_time}")

                    if entry_time >= next_interval_end:
                        with self.data_lock:
                            #logger.warning("ACRONI Acquired data_lock for future baseline entry")
                            try:
                                self.baseline_data_queue.put_nowait(entry)
                                #logger.warning("ACRONI Successfully requeued future baseline entry")
                            except Full:
                                logger.warning("ACRONI Baseline queue full when repopulating")
                        continue

                    if entry_time >= next_interval_start:
                        total_energy = max(total_energy, entry_value)
                        #logger.warning(f"ACRONI Updated total energy to: {total_energy}")

                except Exception as e:
                    logger.warning(f"ACRONI Error processing baseline entry: {str(e)}")
                    continue

            if total_energy > 0:
                try:
                    #logger.warning("ACRONI Preparing to add total energy to queue")
                    timeseries_data = (
                        entry_ts_name,
                        next_interval_start.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                        total_energy
                    )
                    with self.data_lock:
                        #logger.warning("ACRONI Acquired data_lock for adding total energy")
                        self.queue_data.put_nowait(timeseries_data)
                        self.messages.append(timeseries_data)
                        #logger.warning("ACRONI Successfully added total energy to queue")
                except Exception as e:
                    logger.warning(f"ACRONI Failed to add total energy to queue: {str(e)}")

        except Exception as e:
            logger.warning(f"ACRONI Critical error in clear_and_repopulate: {str(e)}")
            self._reset_state()

    def _reset_state(self):
        logger.warning("ACRONI Starting state reset")
        try:
            with self.data_lock:
                logger.warning("ACRONI Acquired data_lock for state reset")
                while not self.queue_data.empty():
                    try:
                        self.queue_data.get_nowait()
                    except Empty:
                        break
                logger.warning("ACRONI Cleared queue_data")

                while not self.baseline_data_queue.empty():
                    try:
                        self.baseline_data_queue.get_nowait()
                    except Empty:
                        break
                logger.warning("ACRONI Cleared baseline_data_queue")

                self.messages = []
                self.energy_before[0] = None
                self.previous_time[0] = None
                self.data_missing_alarm[0] = 0
                self.excess[0] = 0
                self.acroni_cap[0] = datetime.now(timezone.utc)
                logger.warning("ACRONI Reset all state variables")

            logger.warning("ACRONI State reset completed successfully")
        except Exception as e:
            logger.warning(f"ACRONI Failed to reset state: {str(e)}")


    def process(self):

        try:
            interval_start_time_max = datetime.now(timezone.utc)
            logger.warning("Setting up queue manager")
            messages = self.queue_manager.get_messages(preserve=True)
            self.messages = messages

            metrics = self.queue_manager.get_metrics()
            logger.warning(f"Queue metrics: {metrics}")

            logger.warning('Preverjam ali so vse časovne vrste prisotne')
            check_results = self.monitor_timeseries_data()
            logger.warning('Zaključil sem preverjanje ali so vse časovne vrste prisotne')
            value_sum = 0
            timestamp = None
            demag_timestamp = None
            processed_timeseries = set()
            current_interval = None
            interval_end = None
            value_sum_demag = 0
            logging.warning('Začenjam izračun PEAK')

            for message in self.messages:
                timeseries_name, time, numeric_value = message
                current_interval_start = self.get_interval_start_time(time)
                message_interval = self.get_interval_start_time(time)
                if current_interval is None:
                    current_interval = message_interval
                elif message_interval != current_interval or self.is_data_stale():
                    start_hour = interval_start_time_max.replace(minute=0, second=0, microsecond=0)
                    next_hour_start = start_hour + timedelta(hours=1)
                    logging.warning('Berem obračunsko moč')
                    # SIJ_Acroni_Jesenice_Obracunska_moc
                    data = self.ts_api.get_timeseries_data(
                        '1acc970d-007c-413f-93be-f86a3ee4ee8a', start_hour, next_hour_start)
                    if data:
                            max_entry = max(data, key=lambda x: datetime.strptime(
                                x['dateTimeTick'], '%Y-%m-%dT%H:%M:%S.%fZ'))
                            logging.warning('Prebral sem obračunsko moč')
                            self.acroni_power_current_interval[0] = max_entry['numericValue']
                    else:
                        logging.error('Ni podatkov za obračunsko moč')
                        self.acroni_power_current_interval[0] = 0
                    logging.warning('Začel prazniti QUEUE')
                    self.clear_and_repopulate_queue(interval_start_time_max)
                    logging.warning('QUEUE je spraznjen')
                    self.messages = [m for m in self.messages if self.get_interval_start_time(m[1]) == message_interval]

                    value_sum = 0
                    value_sum_demag = 0
                    processed_timeseries.clear()
                    current_interval = message_interval
                    continue

                if timeseries_name in time_series_formula and timeseries_name not in processed_timeseries:
                    min_record, max_record = self.find_min_max_time(timeseries_name, current_interval_start)

                    if min_record and max_record:
                        _, min_time, min_value = min_record
                        _, max_time, max_value = max_record
                        interval_start_time_min = self.get_interval_start_time(min_time)
                        interval_start_time_max = self.get_interval_start_time(max_time)
                        interval_end = interval_start_time_min + \
                            timedelta(minutes=15)

                        adjusted_value = max_value - min_value
                        operation = time_series_formula[timeseries_name]
                        if timeseries_name == "SIJ_Acroni_SI_DEMAG_KumulativnaDelovnaEnergijaPrejeta":
                            value_sum_demag += adjusted_value
                            demag_timestamp = max_time
                        elif timeseries_name == "SIJ_Acroni_SI_DEMAG_KumulativnaDelovnaEnergijaOddana":
                            value_sum_demag -= adjusted_value

                        if operation == "+":
                            value_sum += adjusted_value
                        elif operation == "-":
                            value_sum -= adjusted_value

                        processed_timeseries.add(timeseries_name)

            timestamp = self.find_max_time_all()
            logging.warning('Konec računanja energije')

            if timestamp and self.write_data == 1:
                # Prepare payload for the API
                payload = []
                time_series = [
                    {
                        "dateTimeTick": timestamp,
                        "dateTimeValue": timestamp,
                        "dateTimeToValue": timestamp,
                        "numericValue": value_sum
                    }
                ]
                payload.append({
                    "timeSeriesName": 'SIJ_Acroni_Test2_EnergijaTrenutniObrInterval',
                    "timeSeriesValueInputs": time_series
                })
                try:
                    self.ts_api.save_timeseries_data(payload)
                    payload[0]["timeSeriesName"] = "SIJ_Acroni_Akumulirana_EnergijaTrenutniObrInterval"
                    result = Eventhub.send_payload_to_topic(
                        CONNECTION_STRING, TOPIC, payload)
                except Exception as e:
                    # Log the exception with additional details
                    logging.error("Error saving timeseries data !", str(e))

                thedora = ThedoraTS(self.token)
                df = pd.DataFrame(
                    {'date_time_value': [timestamp], 'numeric_value': [value_sum]})
                thedora_data = thedora.prepare_data(
                    df, value=1, multiplier=1, unit="kWh", timeZone="CET")
                thedora.post_data(thedora_data, 312)
                #self.graph_data.append({"timestamp": timestamp, "value": value_sum})


            logging.warning('Konec pisanja energije v analitično')
            logging.warning(f'Prejšni čas JEBANJA: {self.previous_time[0]}')
            logging.warning(f'Trenutni čas JEBANJA: {timestamp}')
            if timestamp and self.acroni_power_current_interval[0] and interval_end is not None:
                logging.warning('Računam processAcroniAveragePowerEndInterval')
                self.processAcroniAveragePowerEndInterval(value_sum, timestamp, interval_end)
                logging.warning('Izračunal sem processAcroniAveragePowerEndInterval')
            if self.previous_time[0]:
                logging.warning('Računam  processAcroniCurrentWorkingPower')
                power = self.processAcroniCurrentWorkingPower(value_sum, timestamp)
                logging.warning('Računam  calculateWholePower')
                self.calculateWholePower()
                logging.warning('Računam  calculateRestPower')
                self.calculateRestPower()
                logging.warning('Izračunal  calculateRestPower')
                if power > 0:
                    logging.warning('Računam processAcroniCurrentWorkingPowerWithoutDemag')
                    self.processAcroniCurrentWorkingPowerWithoutDemag(
                        power, timestamp, interval_end, value_sum, value_sum_demag, self.previous_demag_time[0], demag_timestamp)
                    logging.warning('Računam processBillingPowerTrendEndoFfInterval')
                    self.processBillingPowerTrendEndoFfInterval(
                        timestamp, interval_end, value_sum)
                    logging.warning('Konec računanja')
            logging.warning('Pišem globalne spremenljivke')
            self.energy_before[0] = value_sum
            self.previous_time[0] = timestamp
            self.demag_energy_before[0] = value_sum_demag
            self.previous_demag_time[0] = demag_timestamp
            logging.warning('Zapisal sem globalne spremenljivke')

        except Empty:
            logging.info("No messages in queue to process")
        except ValueError as ve:
            logging.error(f"Value error during processing: {ve}")
        except TypeError as te:
            logging.error(f"Type error during processing: {te}")
        except Exception as e:
            logging.error(f"Major error in processing Acroni: {str(e)}")
            logging.error(f"Detailed traceback: {traceback.format_exc()}")
            try:
                interval_start = self.get_last_15_minute_interval()
                self.clear_and_repopulate_queue(interval_start)
            except Exception as cleanup_error:
                logging.error(f"Error during cleanup: {cleanup_error}")
        logging.warning('Končal PEAK')

    def get_last_15_minute_interval(self, current_time=None):
        if current_time is None:
            current_time = datetime.now(timezone.utc)

        minutes_to_subtract = current_time.minute % 15
        seconds_to_subtract = current_time.second

        interval_start = current_time - timedelta(minutes=minutes_to_subtract,
                                                seconds=seconds_to_subtract)

        return interval_start.replace(microsecond=0)


    def processAcroniAveragePowerEndInterval(self, energy, current_time, interval_end):
        # Define your date format
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        # Convert current_time and self.previous_time[0] from strings to datetime objects
        current_time_real = datetime.strptime(current_time, time_format)
        billing_energy = (self.acroni_power_current_interval[0] / 4) * 1000
        logging.info(
            "---------------------------------------------------------")
        logging.info("Processing: processAcroniAveragePowerEndInterval")
        logging.info(f"billing_energy = {billing_energy}")
        logging.info(f"current_energy = {energy}")

        delta_E = billing_energy - energy
        delta_t_seconds = (interval_end - current_time_real.replace(
            tzinfo=timezone.utc)).total_seconds()  # Time difference in seconds
        delta_t_hours = delta_t_seconds / 3600  # Convert to hours
        logging.info(f"delta time in seconds  = {delta_t_seconds}")
        logging.info(f"delta time in hours = {delta_t_hours}")
        avg_power_end_interval = 0
        if delta_t_hours > 0 and delta_t_seconds >= 2:  # Ensure no division by zero
            avg_power_end_interval = (
                delta_E / delta_t_hours) / 1000  # Power in MW

        logging.info(f"avg_power_end_interval = {avg_power_end_interval}")
        logging.info("---------------------------------------------------------")
        if self.write_data == 1:
            thedora = ThedoraTS(self.token)
            df = pd.DataFrame(
                {'date_time_value': [current_time], 'numeric_value': [avg_power_end_interval]})
            thedora_data = thedora.prepare_data(
                df, value=0, multiplier=1, unit="MW", timeZone="CET")
            thedora.post_data(thedora_data, 725)
            # Prepare payload for the API
            payload = []
            time_series = [
                {
                    "dateTimeTick": current_time,
                    "dateTimeValue": current_time,
                    "dateTimeToValue": current_time,
                    "numericValue": avg_power_end_interval
                }
            ]
            payload.append({
                "timeSeriesName": 'SIJ_Acroni_SI_PovprecnaDelovnaMocDoKoncaIntervala_MW',
                "timeSeriesValueInputs": time_series
            })
            try:
                self.ts_api.save_timeseries_data(payload)
            except Exception as e:
                # Log the exception with additional details
                logging.error(f"Error saving timeseries data: {str(e)}")

    def processAcroniCurrentWorkingPower(self, energy, current_time):
        # Define your date format
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"

        # Convert current_time and self.previous_time[0] from strings to datetime objects
        current_time_real = datetime.strptime(current_time, time_format)
        previous_time = datetime.strptime(self.previous_time[0], time_format)
        # Calculate the time difference in seconds
        time_delta_seconds = (current_time_real -
                              previous_time).total_seconds()

        # Convert time difference to hours
        time_delta_hours = time_delta_seconds / 3600
        logging.info(
            "---------------------------------------------------------")
        logging.info("Processing: processAcroniCurrentWorkingPower")
        logging.info(f"current_time_real = {current_time_real}")
        logging.info(f"previous_time = {previous_time}")
        logging.info(f"delta time in seconds  = {time_delta_seconds}")
        logging.info(f"time_delta_hours = {time_delta_hours}")
        logging.info(f"energy = {energy}")
        logging.info(f"self.energy_before[0] = {self.energy_before[0]}")

        # Ensure time_delta_hours is not zero to avoid division by zero
        if time_delta_hours != 0 and time_delta_seconds >= 2:
            # Calculate the power in MW (since energy is in kWh and time is now in hours)
            # power = ((energy - self.energy_before[0]) / time_delta_hours) / 1000
            power = self.calculateCurrentPower()
            if power < 0:
                power = 0
            logging.info(f"power = {power}")
            logging.info(
                "---------------------END------------------------------------")
            # Prepare payload for the API
            if self.write_data == 1:
                thedora = ThedoraTS(self.token)
                df = pd.DataFrame(
                    {'date_time_value': [current_time], 'numeric_value': [power]})
                thedora_data = thedora.prepare_data(
                    df, value=0, multiplier=1, unit="MW", timeZone="CET")
                thedora.post_data(thedora_data, 675)
                payload = []
                time_series = [
                    {
                        "dateTimeTick": current_time,
                        "dateTimeValue": current_time,
                        "dateTimeToValue": current_time,
                        "numericValue": power
                    }
                ]
                payload.append({
                    "timeSeriesName": 'SIJ_Acroni_SI_TrenutnaMocObrInterval_MW',
                    "timeSeriesValueInputs": time_series
                })
                try:
                    self.ts_api.save_timeseries_data(payload)
                    # result = Eventhub.send_payload_to_topic(CONNECTION_STRING, TOPIC, payload)
                except Exception as e:
                    # Log the exception with additional details
                    logging.error(f"Error saving timeseries data: {str(e)}")
            self.power_before[0] = power
            return power
        else:
            logging.info("---------------------TIME DIFFERENCE IS ZERO------------------------------------")
            return self.power_before[0]

    def processAcroniCurrentWorkingPowerWithoutDemag(self, power, current_time, interval_end, current_energy,
                                                     current_energy_demag, previous_demag_timestamp, demag_timestamp):
        # Define your date format
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        # Convert current_time and self.previous_time[0] from strings to datetime objects
        current_time_real = datetime.strptime(current_time, time_format)

        try:
            time_delta_seconds = (
                interval_end - current_time_real.replace(tzinfo=timezone.utc)).total_seconds()

            # Convert time difference to hours
            time_delta_hours = time_delta_seconds / 3600

            if previous_demag_timestamp != None:
                demag_curr_power = self.calculateDamagPower(
                    previous_demag_timestamp, demag_timestamp, current_energy_demag)
            else:
                demag_curr_power = 0
            endEnergy = (power - demag_curr_power) * time_delta_hours * 1000
            # realEndEnergy = (current_energy - current_energy_demag) + endEnergy
            if endEnergy < 0:
                realEndEnergy = current_energy
            else:
                realEndEnergy = current_energy + endEnergy
            endPower = realEndEnergy * 4 / 1000
            logging.info("---------------------------------------------------------")
            logging.info("Processing: processAcroniCurrentWorkingPowerWithoutDemag")
            logging.info(f"time_delta_seconds = {time_delta_seconds}")
            logging.info(f"time_delta_hours = {time_delta_hours}")
            logging.info(f"power = {power}")
            logging.info(f"demag_curr_power = {demag_curr_power}")
            logging.info(f"current_energy = {current_energy}")
            logging.info(f"endEnergy = {endEnergy}")
            logging.info(f"realEndEnergy = {realEndEnergy}")
            logging.info(f"endPower = {endPower}")
            logging.info("-----------------------END----------------------------------")
        except Exception as ex:
            logging.error(f"Error calculating processAcroniCurrentWorkingPowerWithoutDemag: {str(ex)}")
            endPower = 0
            realEndEnergy = 0
            demag_curr_power = 0
            return

        if self.write_data == 1:
            thedora = ThedoraTS(self.token)
            df = pd.DataFrame(
                {'date_time_value': [current_time], 'numeric_value': [endPower]})
            thedora_data = thedora.prepare_data(
                df, value=0, multiplier=1, unit="MW", timeZone="CET")
            thedora.post_data(thedora_data, 733)
            df = pd.DataFrame(
                {'date_time_value': [current_time], 'numeric_value': [demag_curr_power]})
            thedora_data = thedora.prepare_data(
                df, value=0, multiplier=1, unit="MW", timeZone="CET")
            thedora.post_data(thedora_data, 620)
            # Prepare payload for the API
            payload = []
            payload_array = []
            time_series = [
                {
                    "dateTimeTick": current_time,
                    "dateTimeValue": current_time,
                    "dateTimeToValue": current_time,
                    "numericValue": endPower
                }
            ]
            payload.append({
                "timeSeriesName": 'SIJ_Acroni_SI_DelovnaMocTrenutniTrendBrezDemagKonecIntervala_MW',
                "timeSeriesValueInputs": time_series
            })
            payload_array.append(payload)
            result = Eventhub.send_payload_to_topic(
                CONNECTION_STRING, TOPIC, payload)
            payload = []
            time_series = [
                {
                    "dateTimeTick": current_time,
                    "dateTimeValue": current_time,
                    "dateTimeToValue": current_time,
                    "numericValue": demag_curr_power
                }
            ]
            payload.append({
                "timeSeriesName": 'SIJ_Acroni_SI_DelovnaMocDemagIzracunana_MW',
                "timeSeriesValueInputs": time_series
            })
            payload_array.append(payload)
            # result = Eventhub.send_payload_to_topic(CONNECTION_STRING, TOPIC, payload)
            pres = 0
            calc_pres = self.acroni_power_current_interval[0] * 0.95
            #fire = 1
            #if self.excess[0] == 0 or self.excess[0] == None:
            #    fire = 0

            if endPower > calc_pres and self.acroni_power_current_interval[0] != None and self.acroni_power_current_interval[0] > 0:
                if self.data_missing_alarm[0] == 0 or self.data_missing_alarm[0] == None:
                    if self.excess[0] == 1:
                        pres = 1
                    else:
                        self.excess[0] = 1
                #if self.excess[0] == 1 and fire == 1:
                #    if self.data_missing_alarm[0] == 0 or self.data_missing_alarm[0] == None:
                #        pres = 1
            else:
                self.excess[0] = 0

            payload = []
            time_series = [
                {
                    "dateTimeTick": current_time,
                    "dateTimeValue": current_time,
                    "dateTimeToValue": current_time,
                    "numericValue": pres
                }
            ]
            payload.append({
                "timeSeriesName": 'SIJ_Acroni_SI_PresezenaObracunskaMocAlarm',
                "timeSeriesValueInputs": time_series
            })
            payload_array.append(payload)
            result = Eventhub.send_payload_to_topic(CONNECTION_STRING, TOPIC, payload)
            try:
                for payload in payload_array:
                    self.ts_api.save_timeseries_data(payload)
            except Exception as e:
                # Log the exception with additional details
                logging.error(f"Error saving timeseries data: {str(e)}")

    def processBillingPowerTrendEndoFfInterval(self, current_time, interval_end, current_energy):
        # Define your date format
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        # Convert current_time and self.previous_time[0] from strings to datetime objects
        current_time_real = datetime.strptime(current_time, time_format)

        # Calculate the time difference in seconds
        time_delta_seconds = (
            interval_end - current_time_real.replace(tzinfo=timezone.utc)).total_seconds()

        # Convert time difference to hours
        time_delta_hours = time_delta_seconds / 3600

        start_interval = interval_end - timedelta(minutes=15)
        pw_time_delta_seconds = (current_time_real.replace(
            tzinfo=timezone.utc) - start_interval).total_seconds()
        pw_time_delta_hours = pw_time_delta_seconds / 3600
        power = current_energy / (pw_time_delta_hours * 1000)

        endEnergy = power * time_delta_hours * 1000
        realEndEnergy = current_energy + endEnergy
        endPower = realEndEnergy * 4 / 1000
        logging.info("---------------------------------------------------------")
        logging.info("Processing: processBillingPowerTrendEndoFfInterval")
        logging.info(f"time_delta_seconds = {time_delta_seconds}")
        logging.info(f"time_delta_hours = {time_delta_hours}")
        logging.info(f"power = {power}")
        logging.info(f"current_energy = {current_energy}")
        logging.info(f"endEnergy = {endEnergy}")
        logging.info(f"realEndEnergy = {realEndEnergy}")
        logging.info(f"endPower = {endPower}")
        logging.info("-----------------------END----------------------------------")

        if self.write_data == 1:
            payload_array = []
            thedora = ThedoraTS(self.token)
            df = pd.DataFrame(
                {'date_time_value': [current_time], 'numeric_value': [endPower]})
            thedora_data = thedora.prepare_data(
                df, value=0, multiplier=1, unit="MW", timeZone="CET")
            thedora.post_data(thedora_data, 730)
            # Prepare payload for the API
            payload = []
            time_series = [
                {
                    "dateTimeTick": current_time,
                    "dateTimeValue": current_time,
                    "dateTimeToValue": current_time,
                    "numericValue": endPower
                }
            ]
            payload.append({
                "timeSeriesName": 'SIJ_Acroni_SI_DelovnaMocTrenutniTrendKonecIntervala_MW',
                "timeSeriesValueInputs": time_series
            })
            payload_array.append(payload)
            df = pd.DataFrame(
                {'date_time_value': [current_time], 'numeric_value': [power]})
            thedora_data = thedora.prepare_data(
                df, value=0, multiplier=1, unit="MW", timeZone="CET")
            thedora.post_data(thedora_data, 738)
            # Prepare payload for the API
            payload = []
            time_series = [
                {
                    "dateTimeTick": current_time,
                    "dateTimeValue": current_time,
                    "dateTimeToValue": current_time,
                    "numericValue": power
                }
            ]
            payload.append({
                "timeSeriesName": 'SIJ_Acroni_SI_PovprecnaDelovnaMocInterval',
                "timeSeriesValueInputs": time_series
            })
            payload_array.append(payload)
            try:
                for payload in payload_array:
                    self.ts_api.save_timeseries_data(payload)
                    if payload[0]["timeSeriesName"] != "SIJ_Acroni_SI_PovprecnaDelovnaMocInterval":
                        result = Eventhub.send_payload_to_topic(
                            CONNECTION_STRING, TOPIC, payload)
            except Exception as e:
                # Log the exception with additional details
                logging.error(f"Error saving timeseries data: {str(e)}")

    def calculateCurrentPower(self):
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        timeseries_dict = {}
        powerSum = 0
        logging.info("Start calc calculateCurrentPower")
        for timeseries_name, timestamp, energy_value in list(self.messages):
            if timeseries_name in time_series_power:
                time_real = datetime.strptime(timestamp, time_format)

                if timeseries_name not in timeseries_dict:
                    timeseries_dict[timeseries_name] = []
                timeseries_dict[timeseries_name].append(
                    (time_real, energy_value))

        for timeseries_name, data in timeseries_dict.items():
            data.sort(key=lambda x: x[0], reverse=True)

            if len(data) < 2:
                continue

            max_time, max_energy = data[0]
            second_max_time, second_max_energy = data[1]

            # Calculate time difference in seconds
            time_diff = (max_time - second_max_time).total_seconds()
            time_diff_hours = time_diff / 3600

            # Calculate energy difference
            energy_diff = (max_energy - second_max_energy) / 1000

            if time_diff > 0:
                power = energy_diff / time_diff_hours
                sign = time_series_power[timeseries_name]
                if sign == "-":
                    power = -power
                powerSum += power
        logging.info(f"End calc calculateCurrentPower. Power is: {powerSum}")
        return powerSum

    def calculateDamagPower(self, previous_demag_timestamp, demag_timestamp, energy):
        # Define your date format
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"

        # Convert current_time and self.previous_time[0] from strings to datetime objects
        current_time_real = datetime.strptime(demag_timestamp, time_format)
        previous_time = datetime.strptime(
            previous_demag_timestamp, time_format)
        # Calculate the time difference in seconds
        time_delta_seconds = (current_time_real -
                              previous_time).total_seconds()

        # Convert time difference to hours
        time_delta_hours = time_delta_seconds / 3600

        logging.info("---------------------------------------------------------")
        logging.info("Processing: calculateDamagPower")
        logging.info(f"current_time_real = {current_time_real}")
        logging.info(f"previous_time = {previous_time}")
        logging.info(f"delta time in seconds  = {time_delta_seconds}")
        logging.info(f"time_delta_hours = {time_delta_hours}")
        logging.info(f"energy = {energy}")
        logging.info(f"self.energy_before[0] = {self.demag_energy_before[0]}")

        try:
            if time_delta_hours is not None and time_delta_hours != 0 and time_delta_seconds >= 2:
                # Calculate the power in MW (since energy is in kWh and time is now in hours)
                power = (
                    (energy - self.demag_energy_before[0]) / time_delta_hours) / 1000
                if power < 0:
                    power = 0
                logging.info(f"power = {power}")
                logging.info("---------------------END------------------------------------")
                return power
                logging.info(f"End calc calculateDamagPower. Power is: {power}")
            else:
                logging.info(f"End calc calculateDamagPower. Power is: 0")
                return 0
        except Exception as ex:
            logging.error(f"Error calculating calculateDamagPower: {str(ex)}")
            return 0


    def calculateWholePower(self):
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        timeseries_dict = {}
        powerSum = 0
        logging.info("Start calc calculateWholePower")
        for timeseries_name, timestamp, energy_value in list(self.messages):
            if timeseries_name in whole_power:
                time_real = datetime.strptime(timestamp, time_format)

                if timeseries_name not in timeseries_dict:
                    timeseries_dict[timeseries_name] = []
                timeseries_dict[timeseries_name].append(
                    (time_real, energy_value))

        for timeseries_name, data in timeseries_dict.items():
            data.sort(key=lambda x: x[0], reverse=True)

            if len(data) < 2:
                continue

            max_time, max_energy = data[0]
            start_time, start_energy = data[-1]

            # Calculate time difference in seconds
            time_diff = (max_time - start_time).total_seconds()
            time_diff_hours = time_diff / 3600

            # Calculate energy difference
            energy_diff = (max_energy - start_energy) / 1000

            if time_diff > 0:
                power = energy_diff / time_diff_hours
                sign = whole_power[timeseries_name]
                if sign == "-":
                    power = -power
                powerSum += power
        logging.info(f"End calc calculateWholePower. Power is: {powerSum}")
        if self.write_data == 1:
            max_time = max_time.strftime(time_format)
            thedora = ThedoraTS(self.token)
            df = pd.DataFrame(
                {'date_time_value': [max_time], 'numeric_value': [powerSum]})
            thedora_data = thedora.prepare_data(
                df, value=0, multiplier=1, unit="MW", timeZone="CET")
            thedora.post_data(thedora_data, 742)
            payload = []
            time_series = [
                {
                    "dateTimeTick": max_time,
                    "dateTimeValue": max_time,
                    "dateTimeToValue": max_time,
                    "numericValue": powerSum
                }
            ]
            payload.append({
                "timeSeriesName": 'SIJ_Acroni_SI_CelotniOdjem',
                "timeSeriesValueInputs": time_series
            })
            try:
                self.ts_api.save_timeseries_data(payload)
                # result = Eventhub.send_payload_to_topic(CONNECTION_STRING, TOPIC, payload)
            except Exception as e:
                # Log the exception with additional details
                logging.error(f"Error saving timeseries data: {str(e)}")

    def calculateRestPower(self):
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        timeseries_dict = {}
        powerSum = 0
        logging.info("Start calc calculateRestPower")
        for timeseries_name, timestamp, energy_value in list(self.messages):
            if timeseries_name in rest_power:
                time_real = datetime.strptime(timestamp, time_format)

                if timeseries_name not in timeseries_dict:
                    timeseries_dict[timeseries_name] = []
                timeseries_dict[timeseries_name].append(
                    (time_real, energy_value))

        for timeseries_name, data in timeseries_dict.items():
            data.sort(key=lambda x: x[0], reverse=True)

            if len(data) < 2:
                continue

            max_time, max_energy = data[0]
            start_time, start_energy = data[-1]

            # Calculate time difference in seconds
            time_diff = (max_time - start_time).total_seconds()
            time_diff_hours = time_diff / 3600

            # Calculate energy difference
            energy_diff = (max_energy - start_energy) / 1000

            if time_diff > 0:
                power = energy_diff / time_diff_hours
                sign = rest_power[timeseries_name]
                if sign == "-":
                    power = -power
                powerSum += power
        logging.info(f"End calc calculateRestPower. Power is: {powerSum}")
        if self.write_data == 1:
            max_time = max_time.strftime(time_format)
            thedora = ThedoraTS(self.token)
            df = pd.DataFrame(
                {'date_time_value': [max_time], 'numeric_value': [powerSum]})
            thedora_data = thedora.prepare_data(
                df, value=0, multiplier=1, unit="MW", timeZone="CET")
            thedora.post_data(thedora_data, 741)
            payload = []
            time_series = [
                {
                    "dateTimeTick": max_time,
                    "dateTimeValue": max_time,
                    "dateTimeToValue": max_time,
                    "numericValue": powerSum
                }
            ]
            payload.append({
                "timeSeriesName": 'SIJ_Acroni_SI_OstaliOdjem',
                "timeSeriesValueInputs": time_series
            })
            try:
                self.ts_api.save_timeseries_data(payload)
                # result = Eventhub.send_payload_to_topic(CONNECTION_STRING, TOPIC, payload)
            except Exception as e:
                # Log the exception with additional details
                logging.error(f"Error saving timeseries data: {str(e)}")

    def monitor_timeseries_data(self):
        time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
        self.data_missing_alarm[0] = 0
        last_update_times = {}
        before_last_update_times = {}
        temp_queue = []
        # Dictionary to keep track of the two highest timestamps for each timeseries
        timeseries_max_times = defaultdict(
            lambda: (None, None))  # (max_time, second_max_time)

        # Process all entries in the queue
        for entry in self.messages:
            try:
                timeseries_name, timestamp, value = entry

                # Store the entry in the temporary list
                temp_queue.append(entry)

                timestamp_real = datetime.strptime(
                    timestamp, time_format).replace(tzinfo=timezone.utc)

                if timeseries_name in self.valid_timeseries_names:
                    current_max, current_second_max = timeseries_max_times[timeseries_name]

                    # Update the max and second max times for this timeseries
                    if current_max is None:
                        timeseries_max_times[timeseries_name] = (
                            timestamp_real, None)
                    elif timestamp_real > current_max:
                        timeseries_max_times[timeseries_name] = (
                            timestamp_real, current_max)
                    elif current_second_max is None or timestamp_real > current_second_max:
                        timeseries_max_times[timeseries_name] = (
                            current_max, timestamp_real)

            except Exception as e:
                logging.error(f"Acroni Error processing queue entry: {e}")

        # After processing all entries, populate the last_update_times and before_last_update_times
        for timeseries_name, (max_time, second_max_time) in timeseries_max_times.items():
            if max_time is not None:
                last_update_times[timeseries_name] = max_time
            if second_max_time is not None:
                before_last_update_times[timeseries_name] = second_max_time

        # If there's no valid time information, return an empty result
        if not timeseries_max_times:
            current_utc = datetime.now(timezone.utc)
            ct = current_utc.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            payloadNone = []
            time_series = [
                {
                    "dateTimeTick": ct,
                    "dateTimeValue": ct,
                    "dateTimeToValue": ct,
                    "numericValue": 1
                }
            ]
            payloadNone.append({
                "timeSeriesName": 'SIJ_Acroni_SI_IzpadPodatkovAlarm',
                "timeSeriesValueInputs": time_series
            })
            try:
                if self.write_data == 1:
                    self.ts_api.save_timeseries_data(payloadNone)
                    result = Eventhub.send_payload_to_topic(
                        CONNECTION_STRING, TOPIC, payloadNone)
                    payloadNone[0]['timeSeriesName'] = 'SIJ_Acroni_SI_IzpadPodatkovOpozorilo'
                    self.ts_api.save_timeseries_data(payloadNone)
                    payload = []
                    time_series = [
                        {
                            "dateTimeTick": ct,
                            "dateTimeValue": ct,
                            "dateTimeToValue": ct,
                            "numericValue": 0
                        }
                    ]
                    payload.append({
                        "timeSeriesName": 'SIJ_Acroni_SI_PresezenaObracunskaMocAlarm',
                        "timeSeriesValueInputs": time_series
                    })
                    result = Eventhub.send_payload_to_topic(
                        CONNECTION_STRING, TOPIC, payload)
                    self.ts_api.save_timeseries_data(payload)

                self.data_missing_alarm[0] = 1

            except Exception as e:
                logging.error(f"Acroni Error saving timeseries data: {str(e)}")
            return []

        results = []
        # Move this outside the loop since it's used multiple times
        current_utc = datetime.now(timezone.utc)

        # Check each timeseries for missing data
        for ts_name in self.valid_timeseries_names:
            last_update = last_update_times.get(ts_name, None)
            before_last_update = before_last_update_times.get(ts_name, None)

            debug = 0
            error = 0
            missing = 0

            if last_update and before_last_update:
                time_between = (last_update - before_last_update).total_seconds()
                time_since_last_update = (current_utc - last_update).total_seconds()

                debug = 1 if time_since_last_update >= 30 or time_between > 15 else 0
                error = 1 if time_between >= 30 or time_since_last_update >= 60 else 0

                if debug == 1:
                    #self.data_missing_alarm[0] = 1
                    logging.error(f"Acroni Timeseries has no data for 15 seconds  = {ts_name}")
                    logging.error("Acroni Time since last update: %s, Current UTC: %s, Last Update: %s, Last Update[-1]: %s",
                                  time_since_last_update, current_utc, last_update, before_last_update)
                if error == 1:
                    self.data_missing_alarm[0] = 1
                    logging.error(f"Acroni Timeseries has no data for 30 seconds  = {ts_name}")
                    logging.error("Acroni Time since last update: %s, Current UTC: %s, Last Update: %s",
                                  time_since_last_update, current_utc, last_update)
            else:
                # For timeseries with no data
                debug = 1
                error = 1
                missing = 1
                self.data_missing_alarm[0] = 1
                logging.error(f"Acroni Timeseries is missing  = {ts_name}")

            # Add result for this timeseries
            results.append({
                "timeseries_name": ts_name,
                "debug": debug,
                "error": error,
                "missing": missing
            })

        # Prepare payload for all results
        ct = current_utc.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        err = any(result["debug"] == 1 or result["error"] == 1 or result["missing"] == 1 for result in results)

        try:
            if self.write_data == 1:
                # Only send to Eventhub if any timeseries has an error
                if any(r["error"] == 1 for r in results):
                    payload = [{
                        "timeSeriesName": 'SIJ_Acroni_SI_IzpadPodatkovAlarm',
                        "timeSeriesValueInputs": [{
                            "dateTimeTick": ct,
                            "dateTimeValue": ct,
                            "dateTimeToValue": ct,
                            "numericValue": 1
                        }]
                    }]
                    self.ts_api.save_timeseries_data(payload)
                    eventhub_result = Eventhub.send_payload_to_topic(
                        CONNECTION_STRING, TOPIC, payload)
                    payload = []
                    time_series = [
                        {
                            "dateTimeTick": ct,
                            "dateTimeValue": ct,
                            "dateTimeToValue": ct,
                            "numericValue": 0
                        }
                    ]
                    payload.append({
                        "timeSeriesName": 'SIJ_Acroni_SI_PresezenaObracunskaMocAlarm',
                        "timeSeriesValueInputs": time_series
                    })
                    result = Eventhub.send_payload_to_topic(
                        CONNECTION_STRING, TOPIC, payload)
                    self.ts_api.save_timeseries_data(payload)

                if any(r["debug"] == 1 for r in results) or any(r["error"] == 1 for r in results):
                    payload = [{
                        "timeSeriesName": 'SIJ_Acroni_SI_IzpadPodatkovOpozorilo',
                        "timeSeriesValueInputs": [{
                            "dateTimeTick": ct,
                            "dateTimeValue": ct,
                            "dateTimeToValue": ct,
                            "numericValue": 1
                        }]
                    }]

                    self.ts_api.save_timeseries_data(payload)
                if all(r["error"] == 0 for r in results):
                    payload = [{
                        "timeSeriesName": 'SIJ_Acroni_SI_IzpadPodatkovAlarm',
                        "timeSeriesValueInputs": [{
                            "dateTimeTick": ct,
                            "dateTimeValue": ct,
                            "dateTimeToValue": ct,
                            "numericValue": 0
                        }]
                    }]
                    self.ts_api.save_timeseries_data(payload)
                    eventhub_result = Eventhub.send_payload_to_topic(
                        CONNECTION_STRING, TOPIC, payload)
                if all(r["debug"] == 0 for r in results):
                    payload = [{
                        "timeSeriesName": 'SIJ_Acroni_SI_IzpadPodatkovOpozorilo',
                        "timeSeriesValueInputs": [{
                            "dateTimeTick": ct,
                            "dateTimeValue": ct,
                            "dateTimeToValue": ct,
                            "numericValue": 0
                        }]
                    }]
                    self.ts_api.save_timeseries_data(payload)


        except Exception as e:
            logging.error(f"Error saving timeseries data for Acroni: {
                          str(e)} + payload: {payload}")

        return results

    def find_min_max_time(self, timeseries_name, interval_start_time):
        min_record = None
        max_record = None

        for data in self.messages:
            current_name, current_time, current_value = data

            if current_name == timeseries_name:
                message_interval_start = self.get_interval_start_time(current_time)

                if message_interval_start == interval_start_time:
                    if min_record is None or current_time < min_record[1]:
                        min_record = data

                    if max_record is None or current_time > max_record[1]:
                        max_record = data

        return min_record, max_record

    def find_max_time_all(self):
        if not self.messages:
            return None
        max_record = max(self.messages, key=lambda data: data[1])

        return max_record[1]

    def is_data_stale(self):
        if not self.messages:
            return False

        current_time = datetime.now(timezone.utc)
        latest_message_time = max(msg[1] for msg in self.messages)

        # Make sure latest_message_time is a datetime object with timezone
        if isinstance(latest_message_time, str):
            latest_message_time = datetime.strptime(latest_message_time, '%Y-%m-%dT%H:%M:%S.%fZ')

        # Make latest_message_time timezone aware
        if latest_message_time.tzinfo is None:
            latest_message_time = latest_message_time.replace(tzinfo=timezone.utc)

        try:
            time_difference = (current_time - latest_message_time).total_seconds() / 60  # in minutes
        except Exception as e:
            logging.error(f"Error calculating time difference: {str(e)}")
            return False

        return time_difference > 2
