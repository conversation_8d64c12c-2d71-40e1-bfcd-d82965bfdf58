import requests
from datetime import datetime, timezone
import logging
import pandas as pd
from dateutil import parser

logger = logging.getLogger(__name__)

class ThedoraTS:
    """
    A class used to represent the time series data posting to Thedora's service.

    Attributes
    ----------
    token : str
        The OAuth access token for authenticating API requests
    """

    def __init__(self, token):
        """
        Initializes the ThedoraTS instance with the provided token and time series mapping.

        Parameters
        ----------
        token : str
            The OAuth access token for authenticating API requests.
        """
        self.token = token
        self.utc_zone = timezone.utc

    def post_data(self, data, thedora_id):
        """
        Posts data to the specified time series in Thedora's service.

        Parameters
        ----------
        ts_id : str
            The ID of the time series to post data to.
        timestamp : str
            The timestamp of the data to post in ISO 8601 format.
        value : float
            The value to post to the time series.

        Returns
        -------
        bool
            True if the data was posted successfully, False otherwise.
        """
        headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }

        url = f'https://services.thedora.io/api/tsm/timeseries/{thedora_id}/data'  # Replace with the actual URL

        try:
            response = requests.post(url, headers=headers, json=data)
            #logger.info(f'Response status for post data: {response.status_code}, thedora_id: {thedora_id}')

            response.raise_for_status()  # Raises HTTPError for bad responses (4xx or 5xx)
            return True
        except requests.exceptions.HTTPError as http_err:
            logger.error("HTTP error occurred: %s", http_err)
            logger.error(f'Response: {response.text}')

            return False
        except requests.exceptions.RequestException as err:
            logger.error("Error occurred: %s", err)
            return False

        except Exception as ex:
            logger.error("Error occurred: %s", ex)
            return False

    def get_data(self, thedora_id, from_datetime, to_datetime):
        """
        Gets data for a specific time series based on the given topic and time range.

        Parameters
        ----------
        topic : str
            The topic name to retrieve data for.
        from_datetime : str
            The start time for the data retrieval in ISO 8601 format.
        to_datetime : str
            The end time for the data retrieval in ISO 8601 format.
        additional_params : dict, optional
            Additional query parameters for the request.

        Returns
        -------
        dict
            The response data if the request was successful, None otherwise.

        """
        headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }

        if not thedora_id:
            logger.error("Time series name '%s' not found in mapping", thedora_id)
            return False

        url = f'https://services.thedora.io/api/tsm/timeseries/{thedora_id}/data'  # Replace with the actual URL
        params = {
            'from': from_datetime,
            'to': to_datetime
        }

        try:
            response = requests.get(url, headers=headers, params=params)
            #logger.info(f'Response status for get data: {response.status_code}, thedora_id: {thedora_id}')

            response.raise_for_status()  # Raises HTTPError for bad responses (4xx or 5xx)
            if response.status_code == 200:
                # Pretvorite JSON vsebino v slovar
                data = response.json()
                return data

        except requests.exceptions.HTTPError as http_err:
            logger.error("HTTP error occurred: %s", http_err)
            logger.error(f'Response: {response.text}')
            return False
        except requests.exceptions.RequestException as err:
            logger.error("Error occurred: %s", err)
            return False

    def format_datetime_string(self, time: datetime) -> str:
        """
        Formats a datetime object as an ISO 8601 string in UTC.

        Parameters
        ----------
        time : datetime
            The datetime object to format.

        Returns
        -------
        str
            The formatted datetime string in ISO 8601 format.
        """
        time_utc = time.astimezone(self.utc_zone)
        return time_utc.strftime('%Y-%m-%dT%H:%M:%SZ')

    def prepare_data(self, df, value, multiplier, unit, timeZone):
        """
        Prepares data for posting to Thedora based on a DataFrame.

        Parameters
        ----------
        df : pd.DataFrame
            The DataFrame containing the data.
        value : int
            The value for the interval.
        multiplier : int
            The multiplier for the interval.
        unit : str
            The unit of measurement.
        timeZone : str
            The time zone of the data.

        Returns
        -------
        dict
            The prepared data for posting.
        """
        if df is None or df.empty:
            logger.error(f"No data to process ")
            return None
        row = df.iloc[0]
        date_time_value = row['date_time_value']
        #numeric_value = float(row['numeric_value']) if row['numeric_value'] is not None else 0.0
        # Check if numeric_value is valid
        numeric_value = row['numeric_value']

        if numeric_value is None or pd.isna(numeric_value):
            logger.info("Numeric value is None or NaN; no data will be posted.")
            return None
        # Convert to float
        try:
            numeric_value = float(numeric_value)
        except ValueError as e:
            logger.error("Error converting numeric_value to float: %s", e)
            return None
        # Ensure the datetime value is timezone-aware
        if isinstance(date_time_value, str):
            # If the string has a 'Z' at the end, it's in UTC
            if 'Z' in date_time_value:
                date_time_value = datetime.fromisoformat(date_time_value.replace('Z', '+00:00'))
            else:
                # Attempt to parse the datetime string
                date_time_value = parser.parse(date_time_value)

        # If date_time_value is naive, make it timezone-aware (e.g., assign UTC)
        if date_time_value.tzinfo is None:
            date_time_value = date_time_value.replace(tzinfo=self.utc_zone)

        formatted_date_time = self.format_datetime_string(date_time_value)

        thedora_data = {
            "interval": {
                "value": value,
                "multiplier": multiplier
            },
            "unit": unit,
            "timeZone": timeZone,
            "data": [{"from": formatted_date_time, "value": numeric_value, "flag": 9}]
        }

        #logger.info(f"Prepared thedora_data: {thedora_data}")
        return thedora_data
