
### 2024-12-16 Divided threads to separated process
<AUTHOR> <br/>
I create 2 separated deployments, since we have a lot of problems with running those processes as single deployment. Process is runned based on env variable `RUN_PROCESS` defined in deployment yaml. Thread is than selected in code:
```python
    if os.getenv("RUN_PROCESS") == "ACRONI":
        logger.info("Starting ACRONI")
        initialize_app("const-acroni.json", credential)
    elif os.getenv("RUN_PROCESS") == "RAVNE":
        logger.info("Starting RAVNE")
        initialize_app("const-ravne.json", credential)
    else:
        logger.info("Starting ACRONI AND RAVNE")
        initialize_app("const.json", credential)
```

Config files on container storage must be updated.

```python

   if const.get("process") == "ACRONI":
        logger.info("STARTING PROCEESS ACRONI")
        # Start the queue processing thread
        queue_thread = threading.Thread(name='Thread-3 (_process_queue)_acroni', target=process_queue, args=(token, ts_api))
        queue_thread.start()
    elif const.get("process") == "RAVNE":
        logger.info("STARTING PROCEESS RAVNE")
        # Start the queue processing thread
        queue_thread_ravne = threading.Thread(name='Thread-4 (_process_queue_ravne)', target=process_queue_ravne, args=(token, ts_api))
        queue_thread_ravne.start()
    else:
        logger.info("STARTING PROCEESS ACRONI AND RAVNE")
        # Start the queue processing thread
        queue_thread = threading.Thread(name='Thread-3 (_process_queue)_acroni', target=process_queue, args=(token, ts_api))
        queue_thread.start()

        # Start the queue processing thread
        queue_thread_ravne = threading.Thread(name='Thread-4 (_process_queue_ravne)', target=process_queue_ravne, args=(token, ts_api))
        queue_thread_ravne.start()

```

This is only temporary solution!!!! Code should be rewritten and later optimized.

FIle deployment-peak -- exists only for case we need to swhitch to older system
