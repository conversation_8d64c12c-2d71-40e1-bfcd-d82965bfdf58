import logging
import json
import pytz
from datetime import datetime, timezone, timedelta
from common import Common as commonlib 
import traceback

logger = logging.getLogger(__name__)

class MeterE850:
    
    def __init__(self, timeseries_data_queue, data_lock):
        """
        Initializes the MeterE850 class.

        Parameters:
            timeseries_data_queue (Queue): A thread-safe queue to store timeseries data.
            data_lock (threading.Lock): A lock for synchronizing access to the queue.
        """
        self.timeseries_data_queue = timeseries_data_queue
        self.data_lock = data_lock
        
    def parse_timestamp(self, ts):
        return datetime.strptime(ts, "%m/%d/%Y %H:%M:%S UTC%z")


    # Method to prepare payload for saving to the time series database
    def manipulate_data_E850(self, data, append_ts):
        """
        Manipulates incoming data and stores it as time series data.

        Args:
            data (dict): The incoming data to be manipulated.
        """
        try:
            payloads_array = []
            payload = []
            data_dict = data
            skip_keys = ['SerijskaStevilkaStevca', 'CasStevcaObOdcitku'] 
            # Iterate through each layer of the JSON structure
            for key, value in data_dict.items():
                if key in skip_keys:
                    continue  # Skip the rest of the loop for this key
                parsed_values = key.split('_')
                name = parsed_values[0]
                unit = parsed_values[2]
                numeric_part = parsed_values[1].replace('d', '.')
                scalar = float(numeric_part)
                # Check if the value is a dictionary (nested layer)
                if isinstance(value, dict):
                    # Iterate through the nested layer
                    for nested_key, nested_value in value.items():
                        # Check if the nested value is a dictionary
                        ts_data = data_dict['CasStevcaObOdcitku'][nested_key]
                        if isinstance(nested_value, dict):
                            # Iterate through the items in the nested dictionary
                            timeseries_name = ''
                            tick = ''
                            dateValue = ''
                            dateValueTo = ''
                            numericValue = ''
                            data_for_ts = False
                            data_type = 0
                            for k, val in nested_value.items():
                                # current consumed work power or 
                                # cumulative consumed work energy or 
                                # current produced work energy
                                # for E850
                                if val in ('7392', '7576') and nested_key[0:4].upper() == 'E850':        
                                    data_type = 1
                                    timeseries_name = nested_key[5:] + '_' + name #+ '_' + unit
                                    if ts_data['1'] == '11200':                          #DLMS reading time
                                        # Convert string to datetime object
                                        timestamp_dt = ts_data['2']
                                        tick_str = commonlib.calculate_utc_time(timestamp_dt, int(ts_data['4']), int(ts_data['7']))
                                        dateValue = tick_str
                                        dateValueTo = tick_str
                                    data_for_ts = True

                                # produced average power in billing interval 
                                # consumed average power in billing interval 
                                # for E850
                                if val in ('45424', '45576') and nested_key[0:4].upper() == 'E850':        
                                    data_type = 2
                                    timeseries_name = nested_key[5:] + '_' + name #+ '_' + unit 
                                    data_for_ts = True

                                if data_for_ts == True and k == '2' and data_type == 1:
                                    numericValue = int (val)

                                if data_for_ts == True and k == '3' and data_type == 1:
                                    mult = self.parse_value(val)
                                    numericValue = round(numericValue * mult * scalar,2)

                                if (data_for_ts == True and k == '2' and data_type == 2 and 'TRENUTNI' in timeseries_name.upper()) or \
                                (data_for_ts == True and k == '3' and data_type == 2 and 'ZADNJI' in timeseries_name.upper()):
                                    numericValue = int (val)
                                    mult = float(nested_value['4']['Scaler'])
                                    numericValue = round(numericValue * mult * scalar,2)

                                    # Convert string to datetime object
                                    timestamp_dt = nested_value['6']
                                    tick_str = commonlib.calculate_utc_time(timestamp_dt, int(ts_data['4']), int(ts_data['7']))
                                    dateValueTo = tick_str
                                    if 'ZADNJI' in timeseries_name.upper():
                                        dateValueTo = tick_str
                                        tick_str = commonlib.calculate_utc_time(timestamp_dt, int(ts_data['4']), int(ts_data['7']), int(nested_value['7']) / 60 * -1)
                                        dateValue = tick_str
                                    else:
                                        dateValue = tick_str
                                        tick_str = commonlib.calculate_utc_time(timestamp_dt, int(ts_data['4']), int(ts_data['7']), int(nested_value['7']) / 60 * 1)
                                        dateValueTo = tick_str

                            if data_for_ts == True:
                                # Create the time series value input
                                current_utc = datetime.now(timezone.utc)
                                tick_str = current_utc.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
                                time_series = [
                                    {
                                        "dateTimeTick": tick_str,
                                        "dateTimeValue": dateValue,
                                        "dateTimeToValue": dateValueTo,
                                        "numericValue": numericValue
                                    }
                                ]
                                payload.append({
                                    "timeSeriesName": append_ts + timeseries_name,
                                    "timeSeriesValueInputs": time_series
                                })
                                payloads_array.append(payload)
                            payload = []
                            data_for_ts = False
                                                    
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON data: {e}")
            logger.error(f"Data is : ' {data}")
            return None

        except Exception as e:
            logger.error(f"Error occured in data processing: {e}")
            logger.error(f"Data is : ' {data}")
            traceback.print_exc()
            return None

        return payloads_array
    def parse_value(self, val):
        # If val is a dictionary, extract the 'Scaler' value
        if isinstance(val, dict):
            scaler = val.get('Scaler', '1')  # Default to '1' if 'Scaler' is not present
        else:
            scaler = val

        # Now scaler is always a string or a number
        if isinstance(scaler, str):
            scaler = scaler.strip("'\" ")  # Remove any surrounding quotes and spaces
            
            try:
                # Try to evaluate as a list
                evaluated = eval(scaler)
                if isinstance(evaluated, list):
                    # If it's a list, return the first element
                    return float(evaluated[0])
                else:
                    # If it's not a list, convert directly to float
                    return float(scaler)
            except:
                # If eval fails, try direct conversion to float
                return float(scaler)
        elif isinstance(scaler, (int, float)):
            return float(scaler)
        elif isinstance(scaler, list):
            # If it's already a list, return the first element
            return float(scaler[0])
        else:
            raise ValueError(f"Unexpected value type: {type(scaler)}")


